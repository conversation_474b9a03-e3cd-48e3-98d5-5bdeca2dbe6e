package config

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

type Config struct {
	WebBackendHost    string `json:"web_backend_host"`
	DashscopeApiKey   string `json:"dashscope_api_key"`
	ProxyURLStr       string `json:"proxy_url"`
	OpenaiApiKey      string `json:"openai_api_key"`
	ReplicateApiToken string `json:"replicate_api_token"`
	FalApiKey         string `json:"fal_api_key"`
	DeepInfraApiKey   string `json:"deepinfra_api_key"`
}

var Cfg Config

func LoadConfig() {
	err := godotenv.Load()
	if err != nil {
		log.Fatalf("Error loading .env file: %v", err)
	}
	log.Printf("Config loaded")

	webBackendHost := os.Getenv("WEB_BACKEND_HOST")
	if webBackendHost == "" {
		log.Fatalf("WEB_BACKEND_HOST environment variable is required")
	}

	dashscopeApiKey := os.Getenv("DASHSCOPE_API_KEY")
	if dashscopeApiKey == "" {
		log.Fatalf("DASHSCOPE_API_KEY environment variable is required")
	}

	proxyURLStr := os.Getenv("PROXY_URL")
	if proxyURLStr == "" {
		log.Printf("No proxy URL provided, using default http client")
	}

	openaiApiKey := os.Getenv("OPENAI_API_KEY")
	if openaiApiKey == "" {
		log.Fatalf("OPENAI_API_KEY environment varialbe is required")
	}

	replicateApiToken := os.Getenv("REPLICATE_API_TOKEN")
	if replicateApiToken == "" {
		log.Fatalf("REPLICATE_API_TOKEN environment variable is required")
	}

	falApiKey := os.Getenv("FAL_API_KEY")
	if falApiKey == "" {
		log.Fatalf("FAL_KEY environment variable is required")
	}

	deepInfraApiKey := os.Getenv("DEEPINFRA_API_KEY")
	if deepInfraApiKey == "" {
		log.Printf("DEEPINFRA_API_KEY environment variable is not set, DeepInfra provider will not be available")
		deepInfraApiKey = ""
	}

	Cfg = Config{
		WebBackendHost:    webBackendHost,
		DashscopeApiKey:   dashscopeApiKey,
		ProxyURLStr:       proxyURLStr,
		OpenaiApiKey:      openaiApiKey,
		ReplicateApiToken: replicateApiToken,
		FalApiKey:         falApiKey,
		DeepInfraApiKey:   deepInfraApiKey,
	}
}
