package handlers

import (
	"context"
	"fmt"
	"log"
	"math"
	"time"

	"github.com/sa<PERSON><PERSON><PERSON>/go-openai"
	"github.com/sasha<PERSON>nov/go-openai/jsonschema"

	"uniscribe-service/internal/constants"
	openaipkg "uniscribe-service/pkg/openai"
)

const GPTModel = openai.GPT4oMini

// TextProcessor 接口定义了文本处理器应该实现的方法
type TextProcessor interface {
	Process(ctx context.Context, text string) (interface{}, error)
}

// CorrectionResult 存储文本纠错结果
type CorrectionResult struct {
	Explanation string `json:"explanation"`
	Output      string `json:"output"`
}

// SummaryResult 存储文本摘要结果
type SummaryResult struct {
	Explanation string `json:"explanation"`
	Output      string `json:"output"`
}

// KeywordResult 存储关键词提取结果
type KeywordResult struct {
	Explanation string   `json:"explanation"`
	Output      []string `json:"output"`
}

// OutlineResult 存储大纲生成结果
type OutlineResult struct {
	Explanation string `json:"explanation"`
	Output      string `json:"output"`
}

type QAResult struct {
	Explanation string   `json:"explanation"`
	Output      []QAPair `json:"output"`
}

type QAPair struct {
	Question string `json:"question"`
	Answer   string `json:"answer"`
}

type CorrectionProcessor struct {
}

func (p *CorrectionProcessor) Process(ctx context.Context, text string) (interface{}, error) {
	schema, err := jsonschema.GenerateSchemaForType(CorrectionResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}
	// 实现文本纠错逻辑
	resp, err := openaipkg.GetClient().CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model:       GPTModel,
		Temperature: 0.1,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: "You are a text processing assistant. Your task is: Correct spelling errors in the text based on context, without any additional refinement.",
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please correct the spelling errors in this text:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "correction_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}

	var result CorrectionResult
	err = schema.Unmarshal(resp.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}
	return result, nil
}

// SummaryProcessor 实现了文本摘要
type SummaryProcessor struct{}

func (p *SummaryProcessor) Process(ctx context.Context, text string) (interface{}, error) {
	schema, err := jsonschema.GenerateSchemaForType(SummaryResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}
	// 实现文本摘要逻辑
	resp, err := openaipkg.GetClient().CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model:       GPTModel,
		Temperature: 0.4,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: "You are a text processing assistant. Your task is: Summarize the text",
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please summarize this text:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "summary_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}
	var result SummaryResult
	err = schema.Unmarshal(resp.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}
	return result, nil
}

// KeywordProcessor 实现了关键词提取。 暂时不用了，代码保留。
type KeywordProcessor struct{}

func (p *KeywordProcessor) Process(ctx context.Context, text string) (interface{}, error) {
	schema, err := jsonschema.GenerateSchemaForType(KeywordResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}
	resp, err := openaipkg.GetClient().CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model:       GPTModel,
		Temperature: 0.2,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: "You are a text processing assistant. Your task is: Extract keywords",
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please extract keywords from this text:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "keywords_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}
	var result KeywordResult
	err = schema.Unmarshal(resp.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}
	return result, nil
}

// OutlineProcessor 实现了大纲生成
type OutlineProcessor struct{}

func (p *OutlineProcessor) Process(ctx context.Context, text string) (interface{}, error) {
	schema, err := jsonschema.GenerateSchemaForType(OutlineResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}

	// 修改系统提示词，明确指定 markdown 格式要求
	systemPrompt := `You are a text processing assistant. Your task is to generate an outline in markdown format.
Follow these strict formatting rules:
1. Use only '#', '##', '###' for headings (no A., B., 1., 2.)
2. Each level should start with the appropriate number of '#'
3. Keep the structure simple and clear
4. Leave a blank line between each heading
Example format:
# Main Topic

## Subtopic 1

### Detail Point 1

### Detail Point 2

## Subtopic 2

### Detail Point 3`

	resp, err := openaipkg.GetClient().CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model:       GPTModel,
		Temperature: 0.2,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: systemPrompt,
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please generate an outline in the original language of the text, strictly following the markdown format:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "outline_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}
	var result OutlineResult
	err = schema.Unmarshal(resp.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}
	return result, nil
}

// QAExtractionProcessor
type QAExtractionProcessor struct{}

func (p *QAExtractionProcessor) Process(ctx context.Context, text string) (interface{}, error) {
	schema, err := jsonschema.GenerateSchemaForType(QAResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}
	resp, err := openaipkg.GetClient().CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model:       GPTModel,
		Temperature: 0.2,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: "You are a text processing assistant. Your task is: Generate Q&A",
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please generate Q&A:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "qa_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}
	var result QAResult
	err = schema.Unmarshal(resp.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}
	return result, nil
}

// ProcessTextTask 函数并行执行所有文本处理任务

type TextTaskProcessor interface {
	Process(ctx context.Context, task Task) error
}

type TextTaskProcessorImpl struct {
	resultSender ResultSender
	retryCount   int
	baseDelay    time.Duration
}

func NewTextTaskProcessor(resultSender ResultSender) TextTaskProcessor {
	return &TextTaskProcessorImpl{resultSender: resultSender, retryCount: 3, baseDelay: 5 * time.Second}
}

func (p *TextTaskProcessorImpl) Process(ctx context.Context, task Task) error {
	log.Printf("receive text task %+v\n", task)
	var processor TextProcessor

	switch task.TaskType {
	case constants.TaskTypeCorrection:
		processor = &CorrectionProcessor{}
	case constants.TaskTypeSummary:
		processor = &SummaryProcessor{}
	case constants.TaskTypeKeyword:
		processor = &KeywordProcessor{}
	case constants.TaskTypeOutline:
		processor = &OutlineProcessor{}
	case constants.TaskTypeQAExtraction:
		processor = &QAExtractionProcessor{}
	default:
		return fmt.Errorf("未知的任务类型: %v", task.TaskType)
	}

	result, err := p.retryTask(ctx, task, processor)
	if err != nil {
		err = p.resultSender.Send(&TaskFailedPyload{
			TaskID:       task.TaskID,
			TaskType:     task.TaskType,
			ErrorMessage: err.Error(),
		})
		if err != nil {
			return fmt.Errorf("failed to send task failed result: %w", err)
		}
		return nil
	}

	switch task.TaskType {
	case constants.TaskTypeCorrection:
		err = p.resultSender.Send(&CorrectionResultPayload{
			TaskID:   task.TaskID,
			TaskType: task.TaskType,
			Text:     result.(CorrectionResult).Output,
		})
	case constants.TaskTypeSummary:
		err = p.resultSender.Send(&SummaryResultPayload{
			TaskID:   task.TaskID,
			TaskType: task.TaskType,
			Summary:  result.(SummaryResult).Output,
		})
	case constants.TaskTypeKeyword:
		err = p.resultSender.Send(&KeywordResultPayload{
			TaskID:   task.TaskID,
			TaskType: task.TaskType,
			Keywords: result.(KeywordResult).Output,
		})
	case constants.TaskTypeOutline:
		err = p.resultSender.Send(&OutlineResultPayload{
			TaskID:   task.TaskID,
			TaskType: task.TaskType,
			Outline:  result.(OutlineResult).Output,
		})
	case constants.TaskTypeQAExtraction:
		err = p.resultSender.Send(&QAResultPayload{
			TaskID:       task.TaskID,
			TaskType:     task.TaskType,
			QAExtraction: result.(QAResult).Output,
		})
	}
	if err != nil {
		return err
	}

	return nil
}

func (p *TextTaskProcessorImpl) retryTask(ctx context.Context, task Task, processor TextProcessor) (interface{}, error) {
	var result interface{}
	var err error

	for attempt := 0; attempt < p.retryCount; attempt++ {
		result, err = processor.Process(ctx, task.TranscriptionText)
		if err == nil {
			return result, nil
		}
		// 指数退避
		sleepTime := p.baseDelay * time.Duration(math.Pow(2, float64(attempt)))
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(sleepTime):
		}
	}
	return nil, fmt.Errorf("failed to process task after %d attempts: %w", p.retryCount, err)
}
