package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"

	"uniscribe-service/internal/constants"
)

type Task struct {
	TaskID                   int64              `json:"taskId"`
	FileUrl                  string             `json:"fileUrl"`
	TaskType                 constants.TaskType `json:"taskType"`
	TranscriptionFileId      int64              `json:"transcriptionFileId"`
	TranscriptionText        string             `json:"transcriptionText"`
	FileDuration             float64            `json:"fileDuration"`
	Language                 string             `json:"language"`
	LanguageCode             string             `json:"languageCode"`
	TranscriptionType        string             `json:"transcriptionType,omitempty"`
	RequestedServiceProvider string             `json:"requestedServiceProvider,omitempty"`
}

type ErrorNoTask struct{}

func (e ErrorNoTask) Error() string {
	return "no task available"
}

func FetchNextTask(apiUrl string) (Task, error) {
	// 从 api 获取任务
	var task Task
	resp, err := http.Post(apiUrl, "application/json", nil)
	if err != nil {
		return task, fmt.Errorf("failed to fetch task: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return task, fmt.Errorf("failed to read response body: %v", err)
		}

		if resp.StatusCode == http.StatusNotFound {
			log.Printf("%s", body)
			return task, ErrorNoTask{}
		}
		return task, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	if err := json.NewDecoder(resp.Body).Decode(&task); err != nil {
		return task, fmt.Errorf("failed to decode response: %v", err)
	}

	return task, nil
}
