package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"uniscribe-service/pkg/fal"
)

const falWhisperAppID = "fal-ai/whisper"

type FalWhisperProvider struct {
	client *fal.Client
}

type FalWhisperChunk struct {
	Timestamp []float64 `json:"timestamp"`
	Text      string    `json:"text"`
	Speaker   string    `json:"speaker,omitempty"`
}

type FalWhisperResult struct {
	Text                string               `json:"text"`
	Chunks              []FalWhisperChunk    `json:"chunks"`
	InferredLanguages   []string             `json:"inferred_languages"`
	DiarizationSegments []DiarizationSegment `json:"diarization_segments,omitempty"`
}

type DiarizationSegment struct {
	Timestamp []float64 `json:"timestamp"`
	Speaker   string    `json:"speaker"`
}

func NewFalWhisperProvider() *FalWhisperProvider {
	return &FalWhisperProvider{
		client: fal.GetClient(),
	}
}

func (f *FalWhisperProvider) Name() string {
	return string(FalWhisper)
}

func (f *FalWhisperProvider) Transcribe(ctx context.Context, task Task) (TranscriptionResult, error) {
	payload := map[string]interface{}{
		"audio_url":   task.FileUrl,
		"task":        "transcribe",
		"diarize":     false, // 当前有问题，不要使用 true
		"chunk_level": "segment",
		"version":     "3",
		"batch_size":  64,
	}

	// 只有在 LanguageCode 不为空时才添加 language 字段
	if task.LanguageCode != "" {
		if task.LanguageCode == "zh_tw" {
			payload["language"] = "zh"
		} else {
			payload["language"] = task.LanguageCode
		}
	}

	// Submit request using the generic client
	resp, err := f.client.QueueRequest(falWhisperAppID, payload)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to submit transcription request: %w", err)
	}

	result, err := f.pollForResult(ctx, resp.RequestID)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to get transcription result: %w", err)
	}

	segments := f.convertToUnifiedSegments(result)

	return TranscriptionResult{
		Text:             result.Text,
		Segments:         segments,
		DetectedLanguage: strings.Join(result.InferredLanguages, ","),
	}, nil
}

func (f *FalWhisperProvider) pollForResult(ctx context.Context, requestID string) (*FalWhisperResult, error) {
	statusChan, errChan := f.client.StreamRequestStatus(falWhisperAppID, requestID, true)

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case err := <-errChan:
			if err != nil {
				return nil, fmt.Errorf("error streaming status: %w", err)
			}
		case status := <-statusChan:
			// Log progress if needed
			for _, logEntry := range status.Logs {
				log.Printf("Fal Whisper log: %s - %s\n", logEntry.Timestamp, logEntry.Message)
			}

			if status.Status == "COMPLETED" {
				// Get final result
				resultBytes, err := f.client.GetRequestResult(falWhisperAppID, requestID)
				if err != nil {
					return nil, fmt.Errorf("failed to get result: %w", err)
				}

				var result FalWhisperResult
				if err := json.Unmarshal(resultBytes, &result); err != nil {
					return nil, fmt.Errorf("failed to unmarshal result: %w", err)
				}

				log.Printf("result: %+v\n", result)
				return &result, nil
			}
		}
	}
}

func (f *FalWhisperProvider) convertToUnifiedSegments(result *FalWhisperResult) []UnifiedSegment {
	segments := make([]UnifiedSegment, len(result.Chunks))
	for i, chunk := range result.Chunks {
		segments[i] = UnifiedSegment{
			ID:        i,
			StartTime: chunk.Timestamp[0],
			EndTime:   chunk.Timestamp[1],
			Text:      chunk.Text,
		}
	}
	return segments
}
