package providers

import (
	"context"
	"fmt"
	"log"
	"strings"

	"uniscribe-service/pkg/deepinfra"
)

const (
	deepInfraWhisperTurboModel = "openai/whisper-large-v3-turbo" // DeepInfra Whisper Turbo 模型
	deepInfraWhisperModel      = "openai/whisper-large-v3"       // DeepInfra Whisper 标准模型
	chunkLevelSegment          = "segment"                       // 段落级别的时间戳
	chunkLevelWord             = "word"                          // 单词级别的时间戳
	taskTypeTranscribe         = "transcribe"                    // 识别任务类型
	taskTypeTranslate          = "translate"                     // 翻译任务类型
)

// DeepInfraWhisperProvider 实现了 Provider 接口
type DeepInfraWhisperProvider struct {
	client        *deepinfra.Client
	model         string
	taskType      string  // 任务类型：transcribe 或 translate
	chunkLevel    string  // 时间戳级别：segment 或 word
	chunkLengthS  int     // 分块长度（秒）
	temperature   float64 // 采样温度
	initialPrompt string  // 初始提示文本
}

// NewDeepInfraWhisperProvider 创建一个新的 DeepInfra Whisper provider
func NewDeepInfraWhisperProvider(modelName string) *DeepInfraWhisperProvider {
	if modelName == "" {
		modelName = deepInfraWhisperTurboModel // 默认使用 turbo 版本
	}
	return &DeepInfraWhisperProvider{
		client:        deepinfra.GetClient(),
		model:         modelName,
		taskType:      taskTypeTranscribe,
		chunkLevel:    chunkLevelSegment, // 默认使用段落级别的时间戳
		chunkLengthS:  30,                // 默认分块长度为 30 秒
		temperature:   0,                 // 默认温度为 0，确定性输出
		initialPrompt: "",                // 默认无初始提示
	}
}

// NewDeepInfraWhisperTurboProvider 创建一个使用 Turbo 模型的 DeepInfra Whisper provider
func NewDeepInfraWhisperTurboProvider() *DeepInfraWhisperProvider {
	return NewDeepInfraWhisperProvider(deepInfraWhisperTurboModel)
}

// NewDeepInfraWhisperStandardProvider 创建一个使用标准模型的 DeepInfra Whisper provider
func NewDeepInfraWhisperStandardProvider() *DeepInfraWhisperProvider {
	return NewDeepInfraWhisperProvider(deepInfraWhisperModel)
}

// Name 返回 provider 的名称
func (d *DeepInfraWhisperProvider) Name() string {
	// 根据使用的模型返回对应的名称
	if d.model == deepInfraWhisperTurboModel {
		return string(DeepInfraWhisperTurbo)
	}
	return string(DeepInfraWhisper)
}

// SetTaskType 设置任务类型
func (d *DeepInfraWhisperProvider) SetTaskType(taskType string) *DeepInfraWhisperProvider {
	if taskType == taskTypeTranscribe || taskType == taskTypeTranslate {
		d.taskType = taskType
	}
	return d
}

// SetChunkLevel 设置时间戳级别
func (d *DeepInfraWhisperProvider) SetChunkLevel(level string) *DeepInfraWhisperProvider {
	if level == chunkLevelWord || level == chunkLevelSegment {
		d.chunkLevel = level
	}
	return d
}

// SetChunkLengthS 设置分块长度（秒）
func (d *DeepInfraWhisperProvider) SetChunkLengthS(seconds int) *DeepInfraWhisperProvider {
	if seconds >= 1 && seconds <= 30 {
		d.chunkLengthS = seconds
	}
	return d
}

// SetTemperature 设置采样温度
func (d *DeepInfraWhisperProvider) SetTemperature(temp float64) *DeepInfraWhisperProvider {
	if temp >= 0 {
		d.temperature = temp
	}
	return d
}

// SetInitialPrompt 设置初始提示文本
func (d *DeepInfraWhisperProvider) SetInitialPrompt(prompt string) *DeepInfraWhisperProvider {
	d.initialPrompt = prompt
	return d
}

// Transcribe 实现语音识别
func (d *DeepInfraWhisperProvider) Transcribe(ctx context.Context, task Task) (TranscriptionResult, error) {
	log.Printf("DeepInfra Whisper transcribing file: %s", task.FileUrl)

	// 从 URL 获取音频数据
	audioData, err := d.client.FetchAudio(task.FileUrl)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to fetch audio file: %w", err)
	}

	// 从 URL 中提取文件名
	urlParts := strings.Split(task.FileUrl, "/")
	filename := urlParts[len(urlParts)-1]
	if filename == "" {
		filename = "audio.mp3" // 默认文件名
	}

	// 准备请求选项
	options := map[string]any{
		"chunk_level":    d.chunkLevel,   // 使用配置的时间戳级别
		"chunk_length_s": d.chunkLengthS, // 使用配置的分块长度
		"temperature":    d.temperature,  // 使用配置的温度
		"task":           d.taskType,     // 使用配置的任务类型
	}

	// 如果有初始提示，添加到选项中
	if d.initialPrompt != "" {
		options["initial_prompt"] = d.initialPrompt
	}

	// 如果指定了语言，添加语言参数
	if task.LanguageCode != "" {
		// DeepInfra Whisper 使用两字母语言代码
		if task.LanguageCode == "zh_tw" || task.LanguageCode == "zh_cn" || task.LanguageCode == "zh" {
			options["language"] = "zh"
		} else if strings.Contains(task.LanguageCode, "_") {
			// 如果是形如 en_US 的格式，只取前面的语言代码
			options["language"] = strings.Split(task.LanguageCode, "_")[0]
		} else {
			options["language"] = task.LanguageCode
		}
	}

	// 调用 DeepInfra API
	resp, err := d.client.TranscribeAudioData(d.model, audioData, filename, options)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("deepinfra whisper transcription failed: %w", err)
	}

	// 转换结果为统一格式
	unifiedSegments := d.convertToUnifiedSegments(resp.Segments)

	return TranscriptionResult{
		Text:             resp.Text,
		Segments:         unifiedSegments,
		DetectedLanguage: resp.Language,
		UsedModel:        AudioModel(d.Name()),
	}, nil
}

// convertToUnifiedSegments 将 DeepInfra Whisper 的片段转换为统一格式
func (d *DeepInfraWhisperProvider) convertToUnifiedSegments(segments []deepinfra.WhisperSegment) []UnifiedSegment {
	unifiedSegments := make([]UnifiedSegment, len(segments))
	for i, segment := range segments {
		// 创建统一格式的片段
		unifiedSegment := UnifiedSegment{
			ID:        segment.ID,
			StartTime: segment.Start,
			EndTime:   segment.End,
			Text:      segment.Text,
			Words:     []Word{},
		}

		// 如果有单词级别的时间戳，转换它们
		if len(segment.Words) > 0 {
			words := make([]Word, len(segment.Words))
			for j, word := range segment.Words {
				words[j] = Word{
					StartTime: word.Start,
					EndTime:   word.End,
					Text:      word.Text,
				}
			}
			unifiedSegment.Words = words
		}

		unifiedSegments[i] = unifiedSegment
	}
	return unifiedSegments
}
