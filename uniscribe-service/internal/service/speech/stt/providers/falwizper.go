package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"uniscribe-service/pkg/fal"
)

const falWizperAppID = "fal-ai/wizper"

type FalWizperProvider struct {
	client *fal.Client
}

type FalWizperChunk struct {
	Timestamp []float64 `json:"timestamp"`
	Text      string    `json:"text"`
	Speaker   string    `json:"speaker,omitempty"`
}

type FalWizperResult struct {
	Text   string           `json:"text"`
	Chunks []FalWizperChunk `json:"chunks"`
}

func NewFalWizperProvider() *FalWizperProvider {
	return &FalWizperProvider{
		client: fal.GetClient(),
	}
}

func (f *FalWizperProvider) Name() string {
	return string(FalWizper)
}

func (f *FalWizperProvider) Transcribe(ctx context.Context, task Task) (TranscriptionResult, error) {
	payload := map[string]interface{}{
		"audio_url":   task.FileUrl,
		"task":        "transcribe",
		"chunk_level": "segment",
		"version":     "3",
	}

	if task.LanguageCode != "" {
		if task.LanguageCode == "zh_tw" {
			payload["language"] = "zh"
		} else {
			payload["language"] = task.LanguageCode
		}
	}

	resp, err := f.client.QueueRequest(falWizperAppID, payload)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to submit transcription request: %w", err)
	}

	result, err := f.pollForResult(ctx, resp.RequestID)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to get transcription result: %w", err)
	}

	segments := f.convertToUnifiedSegments(result)

	return TranscriptionResult{
		Text:     result.Text,
		Segments: segments,
	}, nil
}

func (f *FalWizperProvider) pollForResult(ctx context.Context, requestID string) (*FalWizperResult, error) {
	statusChan, errChan := f.client.StreamRequestStatus(falWizperAppID, requestID, true)

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case err := <-errChan:
			if err != nil {
				return nil, fmt.Errorf("error streaming status: %w", err)
			}
		case status := <-statusChan:
			for _, logEntry := range status.Logs {
				log.Printf("Fal Wizper log: %s - %s\n", logEntry.Timestamp, logEntry.Message)
			}

			if status.Status == "COMPLETED" {
				resultBytes, err := f.client.GetRequestResult(falWizperAppID, requestID)
				if err != nil {
					return nil, fmt.Errorf("failed to get result: %w", err)
				}

				var result FalWizperResult
				if err := json.Unmarshal(resultBytes, &result); err != nil {
					return nil, fmt.Errorf("failed to unmarshal result: %w", err)
				}

				log.Printf("result: %+v\n", result)
				return &result, nil
			}
		}
	}
}

func (f *FalWizperProvider) convertToUnifiedSegments(result *FalWizperResult) []UnifiedSegment {
	segments := make([]UnifiedSegment, len(result.Chunks))
	for i, chunk := range result.Chunks {
		segments[i] = UnifiedSegment{
			ID:        i,
			StartTime: chunk.Timestamp[0],
			EndTime:   chunk.Timestamp[1],
			Text:      chunk.Text,
		}
	}
	return segments
}
