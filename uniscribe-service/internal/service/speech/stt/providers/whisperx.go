package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	replicatepkg "uniscribe-service/pkg/replicate"

	"github.com/replicate/replicate-go"
)

type WhisperXProvider struct {
	client  *replicate.Client
	model   string
	version string
}

func NewWhisperXProvider() *WhisperXProvider {
	return &WhisperXProvider{
		client:  replicatepkg.GetClient(),
		model:   "victor-upmeet/whisperx",
		version: "84d2ad2d6194fe98a17d2b60bef1c7f910c46b2f6fd38996ca457afd9c8abfcb",
	}
}

func (r *WhisperXProvider) Name() string {
	return string(ReplicateWhisperX)
}

type WhisperXOutput struct {
	Segments         []WhisperXSegment `json:"segments"`
	DetectedLanguage string            `json:"detected_language"`
}

type WhisperXSegment struct {
	Start float64        `json:"start"`
	End   float64        `json:"end"`
	Text  string         `json:"text"`
	Words []WhisperXWord `json:"words"`
}

type WhisperXWord struct {
	Start float64 `json:"start"`
	End   float64 `json:"end"`
	Text  string  `json:"word"`
}

func (r *WhisperXProvider) Transcribe(ctx context.Context, task Task) (TranscriptionResult, error) {
	var initialPrompt string
	input := replicate.PredictionInput{
		"audio_file":                   task.FileUrl,
		"debug":                        true,
		"vad_onset":                    0.5,
		"vad_offset":                   0.363,
		"temperature":                  0,
		"align_output":                 false,
		"diarization":                  false,
		"huggingface_access_token":     "*************************************",
		"language_detection_min_prob":  0,
		"language_detection_max_tries": 5,
		"initial_prompt":               initialPrompt,
	}

	if task.LanguageCode != "" {
		if task.LanguageCode == "zh_tw" {
			input["language"] = "zh"
		} else {
			input["language"] = task.LanguageCode
		}
	}

	prediction, err := r.client.Run(ctx, r.model+":"+r.version, input, nil)
	if err != nil {
		return TranscriptionResult{}, err
	}

	text, unifiedSegments, detectedLanguage, err := r.convertWhisperXToUnified(prediction)
	if err != nil {
		return TranscriptionResult{}, err
	}

	return TranscriptionResult{
		Text:             text,
		Segments:         unifiedSegments,
		DetectedLanguage: detectedLanguage,
	}, nil
}

func (r *WhisperXProvider) convertWhisperXToUnified(predictionOutput replicate.PredictionOutput) (string, []UnifiedSegment, string, error) {
	var whisperXOutput WhisperXOutput
	jsonData, err := json.Marshal(predictionOutput)
	if err != nil {
		return "", nil, "", fmt.Errorf("无法序列化预测输出: %v", err)
	}
	err = json.Unmarshal(jsonData, &whisperXOutput)
	if err != nil {
		return "", nil, "", fmt.Errorf("无法解析预测输出: %v", err)
	}

	var text strings.Builder
	var unifiedSegments []UnifiedSegment

	for i, segment := range whisperXOutput.Segments {
		text.WriteString(segment.Text)

		unifiedSegment := UnifiedSegment{
			ID:        i,
			StartTime: segment.Start,
			EndTime:   segment.End,
			Text:      segment.Text,
		}

		for _, word := range segment.Words {
			unifiedWord := Word{
				StartTime:   word.Start,
				EndTime:     word.End,
				Text:        word.Text,
				Punctuation: "",
			}
			unifiedSegment.Words = append(unifiedSegment.Words, unifiedWord)
		}

		unifiedSegments = append(unifiedSegments, unifiedSegment)
	}

	return text.String(), unifiedSegments, whisperXOutput.DetectedLanguage, nil
}
