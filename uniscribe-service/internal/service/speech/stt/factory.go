package stt

import (
	"fmt"

	"uniscribe-service/internal/service/speech/stt/providers"
)

// NewProvider 根据模型类型创建对应的 Provider
func NewProvider(model providers.AudioModel) (providers.Provider, error) {
	switch model {
	case providers.OpenAIWhisper:
		return providers.NewWhisperProvider(), nil
	case providers.AliyunParaformer:
		return providers.NewParaformerProvider(), nil
	case providers.ReplicateWhisperX:
		return providers.NewWhisperXProvider(), nil
	case providers.FalWhisper:
		return providers.NewFalWhisperProvider(), nil
	case providers.FalWizper:
		return providers.NewFalWizperProvider(), nil
	case providers.DeepInfraWhisperTurbo:
		return providers.NewDeepInfraWhisperTurboProvider(), nil
	case providers.DeepInfraWhisper:
		return providers.NewDeepInfraWhisperStandardProvider(), nil
	default:
		return nil, fmt.Errorf("unsupported model: %s", model)
	}
}
