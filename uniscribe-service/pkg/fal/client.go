package fal

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
	"uniscribe-service/internal/config"
)

const (
	baseURL = "https://queue.fal.run"
)

type Client struct {
	httpClient *http.Client
	apiKey     string
}

type RequestStatus struct {
	Status      string         `json:"status"`
	RequestID   string         `json:"request_id"`
	ResponseURL string         `json:"response_url,omitempty"`
	StatusURL   string         `json:"status_url,omitempty"`
	CancelURL   string         `json:"cancel_url,omitempty"`
	Logs        []LogEntry     `json:"logs,omitempty"`
	Metrics     map[string]any `json:"metrics,omitempty"`
}

type LogEntry struct {
	Timestamp string         `json:"timestamp"`
	Message   string         `json:"message"`
	Labels    map[string]any `json:"labels"`
}

type StreamStatus struct {
	Status      string         `json:"status"`
	RequestID   string         `json:"request_id"`
	ResponseURL string         `json:"response_url"`
	StatusURL   string         `json:"status_url"`
	CancelURL   string         `json:"cancel_url"`
	Logs        []LogEntry     `json:"logs"`
	Metrics     map[string]any `json:"metrics"`
}

type QueueResponse struct {
	RequestID string `json:"request_id"`
}

// NewClient creates a new Fal API client
func NewClient(apiKey string) *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: time.Second * 60 * 10,
		},
		apiKey: apiKey,
	}
}

// GetClient returns a singleton instance of the Fal client
func GetClient() *Client {
	return NewClient(config.Cfg.FalApiKey)
}

// QueueRequest submits a request to a specific Fal app
func (c *Client) QueueRequest(appID string, payload map[string]interface{}) (*QueueResponse, error) {
	url := fmt.Sprintf("%s/%s", baseURL, appID)

	body, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request payload: %w", err)
	}

	req, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	var result QueueResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// GetRequestStatus checks the status of a specific request
func (c *Client) GetRequestStatus(appID, requestID string) (*RequestStatus, error) {
	url := fmt.Sprintf("%s/%s/requests/%s/status", baseURL, appID, requestID)

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	var status RequestStatus
	if err := json.NewDecoder(resp.Body).Decode(&status); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &status, nil
}

// GetRequestResult retrieves the raw JSON response of a completed request
func (c *Client) GetRequestResult(appID, requestID string) ([]byte, error) {
	url := fmt.Sprintf("%s/%s/requests/%s", baseURL, appID, requestID)

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to transcribe: status=%d, body=%s", resp.StatusCode, string(body))
	}

	defer resp.Body.Close()

	return io.ReadAll(resp.Body)
}

// CancelRequest attempts to cancel a queued request
func (c *Client) CancelRequest(appID, requestID string) error {
	url := fmt.Sprintf("%s/%s/requests/%s/cancel", baseURL, appID, requestID)

	req, err := http.NewRequest(http.MethodPost, url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to cancel request: status=%d, body=%s", resp.StatusCode, string(body))
	}

	return nil
}

// setHeaders adds the necessary headers to the request
func (c *Client) setHeaders(req *http.Request) {
	req.Header.Set("Authorization", fmt.Sprintf("Key %s", c.apiKey))
	req.Header.Set("Content-Type", "application/json")
}

// StreamRequestStatus streams the status of a request until it's completed
func (c *Client) StreamRequestStatus(appID, requestID string, withLogs bool) (<-chan StreamStatus, <-chan error) {
	statusChan := make(chan StreamStatus)
	errChan := make(chan error, 1)

	url := fmt.Sprintf("%s/%s/requests/%s/status/stream", baseURL, appID, requestID)
	if withLogs {
		url += "?logs=1"
	}

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		errChan <- fmt.Errorf("failed to create request: %w", err)
		close(statusChan)
		close(errChan)
		return statusChan, errChan
	}

	c.setHeaders(req)

	go func() {
		defer close(statusChan)
		defer close(errChan)

		resp, err := c.httpClient.Do(req)
		if err != nil {
			errChan <- fmt.Errorf("failed to send request: %w", err)
			return
		}
		defer resp.Body.Close()

		reader := bufio.NewReader(resp.Body)
		for {
			line, err := reader.ReadString('\n')
			if err != nil {
				if err != io.EOF {
					errChan <- fmt.Errorf("failed to read stream: %w", err)
				}
				return
			}

			line = strings.TrimSpace(line)
			if line == "" || line == ": ping" {
				continue
			}

			if !strings.HasPrefix(line, "data: ") {
				continue
			}

			data := strings.TrimPrefix(line, "data: ")
			var status StreamStatus
			if err := json.Unmarshal([]byte(data), &status); err != nil {
				errChan <- fmt.Errorf("failed to decode status: %w", err)
				return
			}

			statusChan <- status

			if status.Status == "COMPLETED" {
				return
			}
		}
	}()

	return statusChan, errChan
}
