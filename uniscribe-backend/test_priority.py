#!/usr/bin/env python3
"""
测试任务优先级功能的脚本
"""

from models.task import Task
from constants.task import TaskStatus, TaskType

def test_task_priority_ordering():
    """测试任务优先级排序是否正确"""
    
    print("=== 测试任务优先级排序 ===")
    
    # 模拟查询所有待处理任务，按优先级和创建时间排序
    tasks = Task.query.filter_by(status=TaskStatus.pending.id)\
                     .order_by(Task.priority.desc(), Task.created_time.asc())\
                     .limit(10).all()
    
    print(f"找到 {len(tasks)} 个待处理任务:")
    for i, task in enumerate(tasks, 1):
        print(f"{i}. 任务ID: {task.id}, 优先级: {task.priority}, "
              f"类型: {task.task_type}, 创建时间: {task.created_time}")
    
    # 测试转录任务优先级
    print("\n=== 测试转录任务优先级 ===")
    transcription_tasks = Task.query.filter_by(status=TaskStatus.pending.id)\
                                   .filter(Task.task_type.in_([TaskType.transcription.id, TaskType.youtube_download.id]))\
                                   .order_by(Task.priority.desc(), Task.created_time.asc())\
                                   .limit(5).all()
    
    print(f"找到 {len(transcription_tasks)} 个待处理转录任务:")
    for i, task in enumerate(transcription_tasks, 1):
        print(f"{i}. 任务ID: {task.id}, 优先级: {task.priority}, "
              f"类型: {task.task_type}, 创建时间: {task.created_time}")
    
    # 测试文本任务优先级
    print("\n=== 测试文本任务优先级 ===")
    text_task_types = [
        TaskType.correction.id,
        TaskType.summary.id,
        TaskType.keywords.id,
        TaskType.outline.id,
        TaskType.qa_extraction.id,
        TaskType.translation.id,
    ]
    text_tasks = Task.query.filter_by(status=TaskStatus.pending.id)\
                          .filter(Task.task_type.in_(text_task_types))\
                          .order_by(Task.priority.desc(), Task.created_time.asc())\
                          .limit(5).all()
    
    print(f"找到 {len(text_tasks)} 个待处理文本任务:")
    for i, task in enumerate(text_tasks, 1):
        print(f"{i}. 任务ID: {task.id}, 优先级: {task.priority}, "
              f"类型: {task.task_type}, 创建时间: {task.created_time}")

def test_get_next_task_methods():
    """测试获取下一个任务的方法"""
    
    print("\n=== 测试获取下一个任务的方法 ===")
    
    # 测试获取下一个任务（任意类型）
    next_task = Task.get_next_task()
    if next_task:
        print(f"下一个任务: ID={next_task.id}, 优先级={next_task.priority}, "
              f"类型={next_task.task_type}, 创建时间={next_task.created_time}")
    else:
        print("没有找到待处理任务")
    
    # 测试获取下一个转录任务
    next_transcription_task = Task.get_next_transcription_task()
    if next_transcription_task:
        print(f"下一个转录任务: ID={next_transcription_task.id}, "
              f"优先级={next_transcription_task.priority}, "
              f"类型={next_transcription_task.task_type}, "
              f"创建时间={next_transcription_task.created_time}")
    else:
        print("没有找到待处理转录任务")
    
    # 测试获取下一个文本任务
    next_text_task = Task.get_next_text_task()
    if next_text_task:
        print(f"下一个文本任务: ID={next_text_task.id}, "
              f"优先级={next_text_task.priority}, "
              f"类型={next_text_task.task_type}, "
              f"创建时间={next_text_task.created_time}")
    else:
        print("没有找到待处理文本任务")

if __name__ == "__main__":
    # 注意：这个脚本需要在Flask应用上下文中运行
    print("请在Flask应用上下文中运行此脚本")
    print("例如：flask shell 然后 exec(open('test_priority.py').read())")
