# models/purchase.py
from sqlalchemy import func
from . import db


class Purchase(db.Model):
    __tablename__ = "purchases"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.Index("idx_userId", "user_id"),
        db.UniqueConstraint("stripe_payment_id", name="uk_stripe_payment"),
        {"comment": "一次性购买记录表"},
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.BigInteger, nullable=False, comment="用户ID")
    plan_id = db.Column(db.BigInteger, nullable=False, comment="套餐ID")
    quantity = db.Column(db.Integer, default=1, comment="购买数量")
    stripe_payment_id = db.Column(db.String(255), comment="Stripe payment intent id")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    @classmethod
    def get_by_stripe_payment(cls, payment_id):
        return cls.query.filter_by(stripe_payment_id=payment_id).first()

    @classmethod
    def get_by_id(cls, id):
        return cls.query.filter_by(id=id).first()
