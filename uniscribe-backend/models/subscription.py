from sqlalchemy import func

from constants.subscription import SubscriptionStatus

from . import db


class Subscription(db.Model):
    __tablename__ = "subscription"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.Index("idx_user_id", "user_id"),
        db.Index("idx_plan_id", "plan_id"),
        db.UniqueConstraint("stripe_subscription_id"),
        {"comment": "订阅信息表"},
    )

    id = db.Column(
        db.BigInteger, primary_key=True, autoincrement=True, comment="订阅 ID"
    )
    user_id = db.Column(db.BigInteger, nullable=False, index=True, comment="用户 ID")
    plan_id = db.Column(db.BigInteger, nullable=False, index=True, comment="计划 ID")
    stripe_subscription_id = db.Column(
        db.String(255), unique=True, nullable=False, comment="Stripe 订阅 ID"
    )
    status = db.Column(db.String(50), nullable=False, comment="订阅状态")
    current_period_start = db.Column(
        db.TIMESTAMP, nullable=False, comment="当前订阅周期开始时间"
    )
    current_period_end = db.Column(
        db.TIMESTAMP, nullable=False, comment="当前订阅周期结束时间"
    )
    cancel_at_period_end = db.Column(
        db.Boolean, nullable=False, default=False, comment="是否在周期结束时取消"
    )
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    def __repr__(self):
        return f"<Subscription {self.id}>"

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}

    @classmethod
    def get_by_stripe_subscription_id(cls, stripe_subscription_id):
        return cls.query.filter_by(
            stripe_subscription_id=stripe_subscription_id
        ).first()

    @classmethod
    def get_active_subscription_by_user_id(cls, user_id):
        return cls.query.filter_by(
            user_id=user_id, status=SubscriptionStatus.ACTIVE.value
        ).first()

    @classmethod
    def get_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id).all()

    @classmethod
    def get_by_id(cls, id):
        return cls.query.filter_by(id=id).first()
