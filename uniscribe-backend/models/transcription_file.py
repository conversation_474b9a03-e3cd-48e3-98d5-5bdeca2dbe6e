from datetime import datetime

from models import db
from constants.transcription import TranscriptionFileStatus


class TranscriptionFile(db.Model):
    __tablename__ = "transcription_file"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.Index("idx_userId", "user_id"),
        db.Index("idx_fingerprint", "fingerprint"),
        db.Index("idx_createdTime", "created_time"),
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.BigInteger, nullable=False)
    filename = db.Column(db.String(100), nullable=False)
    file_type = db.Column(db.String(20), nullable=False)
    file_url = db.Column(db.String(255), nullable=False)
    file_key = db.Column(db.String(255), nullable=True)
    file_size = db.Column(db.Integer, nullable=False)
    fingerprint = db.Column(db.String(32), nullable=False)
    duration = db.Column(db.Float, nullable=False)
    language_code = db.Column(db.String(20), nullable=True)
    language = db.Column(db.String(40), nullable=True)
    status = db.Column(db.Integer, nullable=False, default=0)
    uploaded_time = db.Column(db.TIMESTAMP, nullable=True)
    is_deleted = db.Column(db.Boolean, nullable=False, default=False)
    created_time = db.Column(db.TIMESTAMP, nullable=False)
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
    )
    transcription_type = db.Column(
        db.String(20),
        nullable=False,
        default="transcript",
        comment="转录类型：transcript/subtitle",
    )
    insufficient_minutes = db.Column(
        db.Integer,
        nullable=False,
        default=0,
        comment="欠费分钟数。0表示未欠费，>0表示欠费分钟数",
    )

    source_type = db.Column(
        db.Enum(
            "upload",
            "youtube",
            name="source_type_enum",
        ),
        nullable=False,
        default="upload",
        comment="文件来源类型：upload/youtube 等",
    )

    source_url = db.Column(
        db.String(255),
        nullable=True,
        comment="媒体来源的URL，根据source_type不同而有不同含义",
    )

    media_download_time = db.Column(
        db.Integer,
        nullable=False,
        default=0,
        comment="媒体下载和上传的总时间（秒），适用于YouTube等外部媒体源",
    )

    def __repr__(self):
        return f"<TranscriptionFile {self.id}>"

    @classmethod
    def get_by_id(cls, id_):
        return cls.query.filter_by(id=id_, is_deleted=False).first()

    @classmethod
    def get_by_fingerprint(cls, fingerprint):
        return cls.query.filter_by(fingerprint=fingerprint, is_deleted=False).first()

    @classmethod
    def get_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id, is_deleted=False).all()

    @classmethod
    def get_by_user_id_and_fingerprint(cls, user_id, fingerprint):
        return cls.query.filter_by(
            user_id=user_id, fingerprint=fingerprint, is_deleted=False
        ).first()

    @classmethod
    def get_by_user_id_and_status(cls, user_id, status):
        return cls.query.filter_by(
            user_id=user_id, status=status, is_deleted=False
        ).all()

    @classmethod
    def list(cls, user_id, after_id, limit, status_list):
        return (
            cls.query.filter(
                cls.id < after_id,
                cls.user_id == user_id,
                cls.status.in_(status_list),
                cls.is_deleted == False,
            )
            .order_by(cls.id.desc())
            .limit(limit)
            .all()
        )

    @classmethod
    def get_files_by_status(cls, status, min_created_time):
        return (
            cls.query.filter(
                cls.status == status,
                cls.created_time >= min_created_time,
                cls.is_deleted == False,
            )
            .order_by(cls.id.desc())
            .all()
        )

    # 计算用户当天转录的文件数量
    @classmethod
    def get_user_today_transcribe_file_count(cls, user_id):
        completed_statuses = [
            TranscriptionFileStatus.processing.id,
            TranscriptionFileStatus.completed.id,
            TranscriptionFileStatus.partially_completed.id,
            TranscriptionFileStatus.completed_with_errors.id,
        ]

        return cls.query.filter(
            cls.user_id == user_id,
            cls.created_time >= datetime.now().date(),
            cls.status.in_(completed_statuses),
            # cls.is_deleted == False,  删除的文件也算
        ).count()

    @classmethod
    def search_by_filename(cls, user_id, filename_keyword, limit=20):
        """
        Search files by filename using MySQL's built-in full Unicode support

        Args:
            user_id: User ID
            filename_keyword: Search keyword (supports all Unicode characters)
            limit: Maximum number of results to return
        """
        return (
            cls.query.filter(
                cls.user_id == user_id,
                cls.filename.like(
                    f"%{filename_keyword}%"
                ),  # MySQL 8.0's ai_ci collation handles case-insensitive
                cls.is_deleted == False,
            )
            .order_by(cls.created_time.desc())
            .limit(limit)
            .all()
        )
