from datetime import datetime
from sqlalchemy.dialects.mysql import LONGTEXT
from models import db


class TaskResult(db.Model):
    __tablename__ = "task_result"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("file_id", name="uniq_fileId"),
    )
    id = db.Column(db.BigInteger, autoincrement=True, comment="明细ID")
    file_id = db.Column(db.BigInteger, nullable=False, comment="转录ID")
    duration = db.Column(db.Float, nullable=False, comment="文件时长")
    language = db.Column(db.String(20), nullable=False, comment="语言")
    detected_language = db.Column(db.String(20), nullable=True, comment="检测语言")
    original_text = db.Column(LONGTEXT, nullable=False, comment="原始转录文本")
    segments = db.Column(db.JSO<PERSON>, nullable=False, comment="分段")
    corrected_text = db.Column(LONGTEXT, nullable=True, comment="校正后文本")
    summary = db.Column(db.Text, nullable=True, comment="摘要")
    keywords = db.Column(db.JSON, nullable=True, comment="关键词")
    outline = db.Column(db.Text, nullable=True, comment="大纲(markdown格式)")
    qa_extraction = db.Column(db.JSON, nullable=True, comment="问答提取")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, default=datetime.now, comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )

    def __repr__(self):
        return f"<TranscriptionResult {self.id}>"

    @classmethod
    def get_by_file_id(cls, file_id):
        """
        根据 file_id 查询 task_result
        """
        return cls.query.filter_by(file_id=file_id).first()
