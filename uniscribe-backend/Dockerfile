
FROM python:3.12.3

WORKDIR /app

# Set timezone to UTC to match production
RUN ln -sf /usr/share/zoneinfo/UTC /etc/localtime

# Install FFmpeg only
RUN apt-get update && \
    apt-get install -y ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# For Chinese users to accelerate the installation of Python packages
ENV \
  PIP_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/ \
  PIP_TRUSTED_HOST=mirrors.aliyun.com

# Install Python dependencies
COPY requirements.txt .
# 安装 uv
RUN pip install uv

# 使用 uv 安装依赖，--system 标志确保安装到系统 Python 环境
RUN uv pip install --system -r requirements.txt
RUN uv pip install --system gunicorn

COPY . .

# Verify FFmpeg installation
RUN ffmpeg -version && ffprobe -version

EXPOSE 8000

CMD ["gunicorn", "--worker-class", "gevent", "--workers", "4", "--timeout", "30",  "--bind", "0.0.0.0:8000", "app:app"]
