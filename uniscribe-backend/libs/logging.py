import threading
from queue import Queue
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime
import pytz
import requests
import os


class AsyncAxiomClient:
    def __init__(
        self,
        token: Optional[str] = None,
        batch_size: int = 100,
        flush_interval: int = 5,
    ):
        self.token = token or os.getenv("AXIOM_TOKEN")
        self.base_url = "https://api.axiom.co/v1"
        self.queue = Queue()
        self.batch_size = batch_size
        self.flush_interval = flush_interval

        # 启动后台线程
        self.worker = threading.Thread(target=self._worker, daemon=True)
        self.worker.start()

    def _worker(self):
        """后台工作线程，批量发送日志"""
        batch = []
        while True:
            try:
                # 等待新的日志事件，但不要永远阻塞
                event = self.queue.get(timeout=self.flush_interval)
                batch.append(event)

                # 如果批次满了或队列为空，就发送
                if len(batch) >= self.batch_size or self.queue.empty():
                    self._send_batch(batch)
                    batch = []
            except Exception:
                # 队列超时，发送剩余的批次
                if batch:
                    self._send_batch(batch)
                    batch = []

    def _send_batch(self, events: List[Dict]):
        if not events:
            return

        try:
            response = requests.post(
                f'{self.base_url}/datasets/{events[0]["dataset"]}/ingest',
                headers={
                    "Authorization": f"Bearer {self.token}",
                    "Content-Type": "application/json",
                },
                json=[e["data"] for e in events],
            )
            response.raise_for_status()
        except Exception as e:
            print(f"Failed to send batch to Axiom: {e}")

    def ingest_events(self, dataset: str, events: List[Dict[str, Any]]) -> None:
        """异步提交日志事件到队列"""
        for event in events:
            self.queue.put({"dataset": dataset, "data": event})

    def flush(self):
        """手动刷新队列中的所有日志"""
        remaining = []
        while not self.queue.empty():
            try:
                remaining.append(self.queue.get_nowait())
            except:
                break
        if remaining:
            self._send_batch(remaining)


class AxiomHandler(logging.Handler):
    def __init__(self, dataset_name: str, token: Optional[str] = None):
        super().__init__()
        self.client = AsyncAxiomClient(token=token)
        self.dataset_name = dataset_name

    def emit(self, record):
        try:
            # 构建基础日志信息
            log_entry = {
                "timestamp": datetime.now(pytz.UTC).isoformat(),
                "level": record.levelname,
                "message": self.format(record),
                "logger": record.name,
                "pathname": record.pathname,
                "function": record.funcName,
                "line_no": record.lineno,
                "environment": os.getenv("APP_SETTINGS", "development"),
                "service": "uniscribe",
            }

            # 从 record.__dict__ 中提取 extra 字段
            extra_fields = {
                "method": record.__dict__.get("method"),
                "path": record.__dict__.get("path"),
                "url": record.__dict__.get("url"),
                "status_code": record.__dict__.get("status_code"),
                "elapsed_time_ms": record.__dict__.get("elapsed_time_ms"),
                "ip": record.__dict__.get("ip"),
                "user_agent": record.__dict__.get("user_agent"),
                "referer": record.__dict__.get("referer"),
                "user_id": record.__dict__.get("user_id"),
            }
            log_entry.update({k: v for k, v in extra_fields.items() if v is not None})

            if record.exc_info:
                log_entry["exception"] = self.formatException(record.exc_info)

            self.client.ingest_events(self.dataset_name, [log_entry])
        except Exception as e:
            print(f"Error sending log to Axiom: {e}")


def setup_logging(
    dataset_name,
    log_level: int = logging.INFO,
    enable_console: bool = True,
):
    """
    设置全局日志配置

    Args:
        dataset_name: Axiom 数据集名称
        log_level: 日志级别
        enable_console: 是否同时输出到控制台

    Returns:
        AxiomHandler: 返回 Axiom handler 实例
    """
    logger = logging.getLogger()
    logger.setLevel(log_level)

    # 清除现有的 handlers
    logger.handlers = []

    # 添加 Axiom handler
    axiom_handler = AxiomHandler(dataset_name=dataset_name)
    axiom_handler.setLevel(log_level)

    # 创建包含行号、文件名和函数名的格式化器
    log_format = (
        "%(asctime)s - %(name)s - %(levelname)s - "
        "[%(filename)s:%(lineno)d] - %(message)s"
    )
    formatter = logging.Formatter(
        log_format,
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    axiom_handler.setFormatter(formatter)
    logger.addHandler(axiom_handler)

    # 可选：同时输出到控制台
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(log_level)
        logger.addHandler(console_handler)

    # 返回 handler 以便注册清理函数
    return axiom_handler
