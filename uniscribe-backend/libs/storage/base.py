from abc import ABC, abstractmethod


class StorageInterface(ABC):
    @abstractmethod
    def generate_presigned_url_for_read(self, key, expired):
        pass

    @abstractmethod
    def generate_presigned_url_for_upload(
        self, key, expired, content_type=None, content_md5_base_64=None
    ):
        pass

    @abstractmethod
    def check_file_exist(self, key):
        pass

    @abstractmethod
    def generate_key(self, user_id, fingerprint, file_type):
        pass

    @abstractmethod
    def generate_file_url(self, key):
        pass

    @abstractmethod
    def permanently_delete_file(self, key):
        pass
