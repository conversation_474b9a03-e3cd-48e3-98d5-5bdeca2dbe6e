from .base import StorageInterface
from qcloud_cos import CosConfig, CosS3Client


class COSStorage(StorageInterface):
    def __init__(self, config):
        self.config = config
        cos_config = CosConfig(
            **config.get_client_kwargs(),
        )
        self.client = CosS3Client(cos_config)

    def generate_presigned_url_for_read(self, key, expired):
        return self.client.get_presigned_download_url(
            Bucket=self.config.get_bucket_name(),
            Key=key,
            Expired=expired,
        )

    def generate_presigned_url_for_upload(
        self, key, expired, content_md5_base_64=None, content_type=None
    ):
        url = self.client.get_presigned_url(
            Bucket=self.config.get_bucket_name(),
            Key=key,
            Method="PUT",
            Expired=expired,
            Headers={"Content-MD5": content_md5_base_64},
        )
        return url

    def check_file_exist(self, key):
        return self.client.object_exists(
            Bucket=self.config.get_bucket_name(),
            Key=key,
        )

    def generate_key(self, user_id, fingerprint, file_type):
        return f"{user_id}-{fingerprint}.{file_type}"

    def generate_file_url(self, key):
        return f"{self.config.get_endpoint_url()}/{key}"

    def permanently_delete_file(self, key):
        """
        删除指定的文件(仅当注销账号时使用)

        :param key: 文件的键（路径）
        """
        pass
