from threading import Thread
from queue import Queue
import logging
from typing import Dict, Any
import time
from dataclasses import dataclass
from datetime import datetime


logger = logging.getLogger(__name__)


@dataclass
class Task:
    id: str
    status: str  # pending, processing, completed, failed
    created_at: datetime
    updated_at: datetime
    result: Any = None
    error: str = None


class TaskManager:
    def __init__(self):
        self.task_queue = Queue()
        self.tasks: Dict[str, Task] = {}
        self._start_worker()

    def _start_worker(self):
        def worker():
            while True:
                try:
                    task_id, func, args, kwargs = self.task_queue.get()
                    if task_id in self.tasks:
                        self.tasks[task_id].status = "processing"
                        self.tasks[task_id].updated_at = datetime.now()
                        try:
                            result = func(*args, **kwargs)
                            self.tasks[task_id].status = "completed"
                            self.tasks[task_id].result = result
                        except Exception as e:
                            logger.error(f"Task {task_id} failed: {str(e)}")
                            self.tasks[task_id].status = "failed"
                            self.tasks[task_id].error = str(e)
                        finally:
                            self.tasks[task_id].updated_at = datetime.now()
                except Exception as e:
                    logger.error(f"Worker error: {str(e)}")
                finally:
                    self.task_queue.task_done()

        thread = Thread(target=worker, daemon=True)
        thread.start()

    def submit_task(self, func, *args, **kwargs) -> str:
        task_id = str(int(time.time() * 1000))  # 简单的任务ID生成
        self.tasks[task_id] = Task(
            id=task_id,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        self.task_queue.put((task_id, func, args, kwargs))
        return task_id

    def get_task_status(self, task_id: str) -> Dict:
        task = self.tasks.get(task_id)
        if not task:
            return None
        return {
            "id": task.id,
            "status": task.status,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat(),
            "result": task.result,
            "error": task.error,
        }
