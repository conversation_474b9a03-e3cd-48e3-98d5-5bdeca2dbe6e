"""Flask 应用工厂模块"""

import time
import logging
from flask import Flask, request, g
from pyinstrument import Profiler

from models import db
from resources import api, register_error_handlers
from routes import register_routes
from config import CONFIG, is_development_env, is_production_env, env
from config_modules.cors import setup_cors
from config_modules.logging import setup_app_logging
from config_modules.sentry import init_sentry
from libs.storage.factory import StorageFactory
from libs.task_manager import TaskManager
from cli import register_all_commands


def create_app():
    """创建 Flask 应用"""
    app = Flask(__name__)
    app.config.from_object(CONFIG)

    # 初始化 Sentry（仅在生产环境）
    if is_production_env():
        init_sentry(env)

    # 设置日志
    logger = setup_app_logging(CONFIG)

    # 设置 CORS
    setup_cors(app)

    # 初始化各种组件
    db.init_app(app)
    register_routes(api)
    register_error_handlers()
    api.init_app(app)

    # 注册 CLI 命令
    register_all_commands(app)

    # 初始化存储
    _setup_storage(app)

    # 初始化任务管理器
    app.task_manager = TaskManager()

    # 创建数据库表
    with app.app_context():
        db.create_all()

    # 注册请求钩子
    _register_request_hooks(app, logger)

    if is_development_env():
        _print_app_config(app)

    return app


def _setup_storage(app):
    """设置存储配置"""
    storage_type = app.config.get("STORAGE_TYPE")
    if storage_type == "s3":
        storage_config = app.config.get("CLOUDFLARE_R2")
    elif storage_type == "cos":
        storage_config = app.config.get("TENCENT_CLOUD")
    else:
        raise ValueError(f"Unsupported storage type: {storage_type}")

    app.storage = StorageFactory.get_storage(storage_type, storage_config)


def _register_request_hooks(app, logger):
    """注册请求钩子"""

    @app.before_request
    def before_request():
        request.start_time = time.time()
        if is_development_env() and "profile" in request.args:
            g.profiler = Profiler()
            g.profiler.start()

    @app.after_request
    def after_request(response):
        end_time = time.time()
        elapsed_time = end_time - request.start_time
        user_id = getattr(g.user, "id", None) if hasattr(g, "user") else None

        # 记录请求日志
        logger.info(
            "%s %s",
            request.method,
            request.path,
            extra={
                "ip": request.remote_addr,
                "method": request.method,
                "path": request.path,
                "url": request.url,
                "user_id": str(user_id) if user_id else None,
                "status_code": response.status_code,
                "elapsed_time_ms": round(elapsed_time * 1000, 2),
                "user_agent": request.headers.get("User-Agent"),
                "referer": request.headers.get("Referer"),
                "query_string": request.query_string.decode("utf-8"),
            },
        )

        if hasattr(g, "profiler"):
            g.profiler.stop()
            g.profiler.open_in_browser()

        return response


def _print_app_config(app):
    """打印应用配置（仅开发环境）"""
    import os

    print(f"current env: {os.getenv('APP_SETTINGS')}")
    if not is_development_env():
        print("Not in development environment, skipping config print")
        return
    print("Flask App Configuration:")
    for key in app.config:
        print(f"{key} = {app.config[key]}")
