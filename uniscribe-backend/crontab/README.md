# 定时任务说明

本目录包含需要定期执行的定时任务脚本。

## 文件清理任务

`file_cleanup.py` - 用于定期清理过期文件和引用计数为0的文件。

建议每天运行一次：

```bash
0 0 * * * cd /path/to/uniscribe-backend && python -m crontab.file_cleanup
```

## AppSumo 邮件发送任务

`appsumo_email_sender.py` - 用于定期向 AppSumo 用户发送后续邮件。

该脚本会自动处理以下邮件发送：

1. **Follow-up Reminder 邮件**：在用户首次激活后 2 天发送
2. **Last Touch 邮件**：在 Follow-up 邮件发送后 7 天发送

建议每天运行一次：

```bash
0 1 * * * cd /path/to/uniscribe-backend && python -m crontab.appsumo_email_sender
```

## 邮件发送流程

AppSumo 用户邮件发送流程如下：

1. **首次激活**：用户激活 AppSumo license 时，立即发送 Onboarding 邮件
2. **第二次触点**：首次激活后 2 天，发送 Follow-up Reminder 邮件
3. **第三次触点**：第二次触点后 7 天，发送 Last Touch 邮件

所有邮件发送状态都会记录在 Redis 缓存中，以避免重复发送。缓存键格式如下：

- Onboarding 邮件：`appsumo_email:onboarding:{user_id}`
- Follow-up 邮件：`appsumo_email:follow_up:{user_id}`
- Last Touch 邮件：`appsumo_email:last_touch:{user_id}`

## 手动运行

如需手动运行任务，可以使用以下命令：

```bash
# 文件清理任务
python -m crontab.file_cleanup

# AppSumo 邮件发送任务
python -m crontab.appsumo_email_sender
```

确保在运行命令前已激活正确的 Python 环境。
