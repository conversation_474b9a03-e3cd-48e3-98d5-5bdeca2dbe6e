import os
from abc import ABC, abstractmethod
from io import BytesIO
from urllib.parse import quote


from flask import send_file, make_response
from flask_restful import abort
from reportlab.platypus import SimpleDocTemplate, Paragraph
from reportlab.lib.styles import ParagraphStyle
from reportlab.lib.pagesizes import letter
from reportlab.lib.units import cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from docx import Document
from docx.shared import Pt
from docx.oxml.ns import qn
import csv
from io import StringIO


from models.task_result import TaskResult
from models.transcription_file import TranscriptionFile


class Export(ABC):
    @abstractmethod
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        pass

    def _format_timestamp(self, start_time):
        """格式化时间戳为 mm:ss 格式"""
        minutes = int(start_time) // 60
        seconds = int(start_time) % 60
        return f"{int(minutes):02d}:{int(seconds):02d}"

    def _format_segment_content(
        self, segment, show_speaker_name=True, show_timestamps=True
    ):
        """格式化每个片段的内容"""
        speaker = segment.get("speaker", "Speaker")
        text = segment.get("text", "").strip()

        output = []

        # 构建头部信息（说话人和时间戳）
        header_parts = []
        if show_speaker_name and speaker:
            header_parts.append(speaker)
        if show_timestamps:
            timestamp = self._format_timestamp(segment.get("start_time", 0))
            header_parts.append(timestamp)

        # 如果有头部信息，添加到输出
        if header_parts:
            output.append("  ".join(header_parts))

        if text:
            output.append(text)
            output.append("")  # Empty line between segments

        return output


class PDFExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        temp_filename = "temp_output.pdf"
        doc = SimpleDocTemplate(
            temp_filename,
            pagesize=letter,
            leftMargin=2 * cm,
            rightMargin=2 * cm,
            topMargin=2 * cm,
            bottomMargin=2 * cm,
        )

        pdfmetrics.registerFont(
            TTFont("SourceHanSerif", "fonts/SourceHanSerifCN-VF.ttf")
        )

        custom_style = ParagraphStyle(
            "CustomStyle",
            fontName="SourceHanSerif",
            fontSize=10,
            leading=16,
            spaceAfter=6,
        )

        output = []
        for segment in data:
            output.extend(
                self._format_segment_content(
                    segment, show_speaker_name, show_timestamps
                )
            )

        paragraphs = [Paragraph(line, custom_style) for line in output if line.strip()]

        doc.build(paragraphs)

        with open(temp_filename, "rb") as file:
            pdf_content = file.read()

        os.remove(temp_filename)
        return pdf_content


class DocxExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        doc = Document()
        style = doc.styles["Normal"]
        font = style.font
        font.name = "Source Han Serif"
        font.size = Pt(10)

        rFonts = style.element.rPr.rFonts
        rFonts.set(qn("w:eastAsia"), "Source Han Serif")

        for segment in data:
            content = self._format_segment_content(
                segment, show_speaker_name, show_timestamps
            )
            for line in content:
                if line:  # Skip empty lines
                    paragraph = doc.add_paragraph(line)
                    run = paragraph.runs[0]
                    run.font.name = "Source Han Serif"
                    run.font.size = Pt(10)
                    rFonts = run._element.rPr.rFonts
                    rFonts.set(qn("w:eastAsia"), "Source Han Serif")

        temp_filename = "temp_output.docx"
        doc.save(temp_filename)

        with open(temp_filename, "rb") as file:
            docx_content = file.read()

        os.remove(temp_filename)
        return docx_content


class TxtExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        output = []
        for segment in data:
            output.extend(
                self._format_segment_content(
                    segment, show_speaker_name, show_timestamps
                )
            )
        return "\n".join(output).encode("utf-8")


class SRTExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        srt_content = ""
        for index, item in enumerate(data, start=1):
            start_time = self._format_time(item["start_time"])
            end_time = self._format_time(item["end_time"])

            # 构建字幕文本
            text = item["text"]
            if show_speaker_name and item.get("speaker"):
                text = f"{item['speaker']}: {text}"

            srt_content += f"{index}\n{start_time} --> {end_time}\n{text}\n\n"
        return srt_content.encode("utf-8")

    def _format_time(self, seconds):
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        milliseconds = int((seconds - int(seconds)) * 1000)
        return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"


class VTTExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        vtt_content = "WEBVTT\n\n"  # WebVTT header
        for item in data:
            start_time = self._format_time(item["start_time"])
            end_time = self._format_time(item["end_time"])
            text = item["text"]

            # 如果有说话人，添加到字幕中
            if show_speaker_name and item.get("speaker"):
                text = f"{item['speaker']}: {text}"

            vtt_content += f"{start_time} --> {end_time}\n{text}\n\n"
        return vtt_content.encode("utf-8")

    def _format_time(self, seconds):
        """格式化时间为 WebVTT 格式 (HH:MM:SS.mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds_remainder = seconds % 60
        milliseconds = int((seconds_remainder - int(seconds_remainder)) * 1000)
        return (
            f"{hours:02d}:{minutes:02d}:{int(seconds_remainder):02d}.{milliseconds:03d}"
        )


class CSVExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        output = StringIO()
        writer = csv.writer(output)

        # Write CSV headers in English
        headers = []
        if show_timestamps:
            headers.extend(["Start Time", "End Time"])
        if show_speaker_name:
            headers.append("Speaker")
        headers.append("Text")
        writer.writerow(headers)

        # Write data rows
        for segment in data:
            row = []
            if show_timestamps:
                row.extend(
                    [
                        self._format_time(segment.get("start_time", 0)),
                        self._format_time(segment.get("end_time", 0)),
                    ]
                )
            if show_speaker_name:
                row.append(segment.get("speaker", "Speaker"))
            row.append(segment.get("text", "").strip())
            writer.writerow(row)

        return output.getvalue().encode("utf-8-sig")  # Use BOM for Excel compatibility

    def _format_time(self, seconds):
        """Format time as HH:MM:SS.mmm"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds_remainder = seconds % 60
        milliseconds = int((seconds_remainder - int(seconds_remainder)) * 1000)
        return (
            f"{hours:02d}:{minutes:02d}:{int(seconds_remainder):02d}.{milliseconds:03d}"
        )


# 根据文件类型选择不同的导出格式
def get_exporter(file_type):
    if file_type == "pdf":
        return PDFExport()
    elif file_type == "docx":
        return DocxExport()
    elif file_type == "txt":
        return TxtExport()
    elif file_type == "srt":
        return SRTExport()
    elif file_type == "vtt":  # 添加 VTT 支持
        return VTTExport()
    elif file_type == "csv":
        return CSVExport()
    else:
        raise NotImplementedError(f"不支持的文件类型: {file_type}")


def create_download_response(
    file_content, filename, mime_type="application/octet-stream"
):
    """
    创建文件下载响应

    Args:
        file_content (bytes): 文件内容
        filename (str): 文件名
        mime_type (str, optional): MIME类型. 默认为 "application/octet-stream"

    Returns:
        Response: Flask响应对象
    """
    # 创建一个BytesIO对象
    file_buffer = BytesIO(file_content)
    file_buffer.seek(0)

    filename_encoded = quote(filename)

    # 创建响应
    response = make_response(
        send_file(
            file_buffer,
            mimetype=mime_type,
            as_attachment=True,
            download_name=filename_encoded,
        )
    )

    # 设置不缓存的头部
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    # 显式设置 Content-Disposition 头
    response.headers["Access-Control-Expose-Headers"] = "Content-Disposition"
    response.headers["Content-Disposition"] = (
        f"attachment; filename*=UTF-8''{filename_encoded}"
    )

    return response


def export_text(file_type, file_id, show_speaker_name=True, show_timestamps=True):
    exporter = get_exporter(file_type)

    # 将字符串 ID 转换为整数
    try:
        file_id_int = int(file_id)
    except (ValueError, TypeError):
        abort(400, message="Invalid file ID")

    transcription_file = TranscriptionFile.get_by_id(file_id_int)
    if not transcription_file:
        abort(404, message="未找到转录文件")

    task_result = TaskResult.get_by_file_id(file_id_int)
    if not task_result:
        abort(404, message="未找到任务结果")

    filename = f"{transcription_file.filename}.{file_type}"

    # Check if user has insufficient minutes
    segments = task_result.segments
    if transcription_file.insufficient_minutes > 0:
        # Calculate cutoff time in seconds
        cutoff_time = (
            transcription_file.duration - transcription_file.insufficient_minutes * 60
        )

        # Filter segments to only include those within the allowed time
        filtered_segments = [
            seg for seg in segments if seg.get("end_time", 0) <= cutoff_time
        ]

        # Create notification message
        notification_msg = {
            "start_time": cutoff_time,
            "end_time": cutoff_time + 1,
            "text": f"You've run out of transcription time. This content has {transcription_file.insufficient_minutes} more minutes that need to be unlocked. Upgrade to see everything.",
        }

        # Add notification to the end of filtered segments
        filtered_segments.append(notification_msg)

        segments = filtered_segments

    file_content = exporter.export(segments, show_speaker_name, show_timestamps)

    return create_download_response(file_content, filename)
