from datetime import datetime
import time
import logging
from pprint import pformat

from flask_restful import abort

from constants.subscription import SubscriptionStatus
from libs.utils import convert_unixtime_to_datetime
from libs.id_generator import id_generator
from models import db_transaction, insert_record
from models.plan import Plan
from models.user import User
from models import db
from models.subscription import Subscription
from services.queue_service import QueueService
from services.stripe_service import StripeService, stripe
from config import CONFIG
from services.entitlement_service import EntitlementService
from models.purchase import Purchase

logger = logging.getLogger(__name__)


class StripeController:

    @staticmethod
    def create_checkout_session(
        user,
        mode,
        plan,
        quantity,
        promotion_code=None,
    ):
        try:
            assert user.email is not None
            customer_email = user.email
            if not user.stripe_customer_id:
                stripe_customer_id = StripeService.create_customer(customer_email)
                User.set_stripe_customer_id(user.id, stripe_customer_id)

            # validate plan and mode
            if mode not in ["subscription", "payment"]:
                abort(400, message="Invalid mode")

            if plan not in CONFIG.STRIPE_PLAN_PRICE_LOOKUP_KEYS:
                abort(400, message="Invalid plan")

            # TODO：检查用户是否已经订阅了该计划

            price_lookup_key = CONFIG.STRIPE_PLAN_PRICE_LOOKUP_KEYS[plan]
            logger.info("price_lookup_key: %s", price_lookup_key)
            price_id = StripeService.get_price_id_by_lookup_key(price_lookup_key)

            if quantity is None:
                quantity = 1

            # Add promotion code to checkout session if provided
            checkout_params = {
                "price_id": price_id,
                "quantity": quantity,
                "mode": mode,
                "customer_id": user.stripe_customer_id,
            }

            # 处理 promo code
            promo_code_id = None

            # 1. 如果前端传来 promo code，则检验，有效则传给 stripe
            if promotion_code:
                is_valid, promo_id = StripeController.validate_promo_code(
                    promotion_code, plan
                )
                if is_valid:
                    promo_code_id = promo_id
                    logger.info(
                        "Using provided promo code: %s for plan: %s",
                        promotion_code,
                        plan,
                    )
                else:
                    logger.error(
                        "Invalid promo code provided: %s for plan: %s",
                        promotion_code,
                        plan,
                    )

            # 2. 如果前端没有传 promo code 或提供的 promo code 无效，则检查配置中是否有有效的 promo code
            if not promo_code_id and plan in CONFIG.STRIPE_PROMO_CODE_LOOKUP_KEYS:
                config_promo_code = CONFIG.STRIPE_PROMO_CODE_LOOKUP_KEYS[plan]
                is_valid, promo_id = StripeController.validate_promo_code(
                    config_promo_code, plan
                )
                if is_valid:
                    promo_code_id = promo_id
                    logger.info(
                        "Using config promo code: %s for plan: %s",
                        config_promo_code,
                        plan,
                    )
                else:
                    logger.error(
                        "Config promo code is not valid: %s for plan: %s",
                        config_promo_code,
                        plan,
                    )

            # 如果有有效的 promo code ID，添加到 checkout 参数中
            if promo_code_id:
                checkout_params["discounts"] = [{"promotion_code": promo_code_id}]

            return StripeService.create_checkout_session(
                price_id=price_id,
                quantity=quantity,
                mode=mode,
                customer_id=user.stripe_customer_id,
                discounts=checkout_params.get("discounts"),
            )
        except Exception as e:
            logger.error("Error creating checkout session: %s", str(e), exc_info=True)
            abort(500, message="Failed to create checkout session")

    @staticmethod
    def cancel_unpaid_invoice(user):
        pass
        StripeService.cancel_unpaid_invoice(user.stripe_customer_id)

    @staticmethod
    def create_portal_session(user):
        if not user.stripe_customer_id:
            stripe_customer_id = StripeService.create_customer(user.email)
            User.set_stripe_customer_id(user.id, stripe_customer_id)
            user = User.get_by_id(user.id)
        return StripeService.create_portal_session(user.stripe_customer_id)

    @staticmethod
    def handle_webhook(payload, sig_header):
        event = StripeService.handle_webhook(payload, sig_header)
        logger.info("Received stripe event: \n%s", pformat(event.to_dict()))

        queue_data = {"event_type": event.type, "event_data": event.to_dict()}
        QueueService.enqueue("stripe_events", queue_data)

        return {"status": "queued"}

    @staticmethod
    def create_subscription(stripe_subscription):
        # Get user from customer ID
        user = User.query.filter_by(
            stripe_customer_id=stripe_subscription.customer
        ).first()
        if not user:
            logger.error(
                "No user found for customer %s",
                stripe_subscription.customer,
                exc_info=True,
            )
            return None

        # Get plan from price ID
        plan = Plan.get_by_stripe_price_id(stripe_subscription.plan.id)
        if not plan:
            logger.error(
                "No plan found for price %s", stripe_subscription.plan.id, exc_info=True
            )
            return None

        # Create subscription record
        subscription = Subscription(
            id=id_generator.get_id(),
            user_id=user.id,
            plan_id=plan.id,
            stripe_subscription_id=stripe_subscription.id,
            status=stripe_subscription.status,
            current_period_start=convert_unixtime_to_datetime(
                stripe_subscription.current_period_start
            ),
            current_period_end=convert_unixtime_to_datetime(
                stripe_subscription.current_period_end
            ),
            cancel_at_period_end=stripe_subscription.cancel_at_period_end,
        )
        insert_record(subscription)
        return subscription

    @db_transaction()
    def handle_subscription_created(event):
        stripe_subscription = event.data.object

        # Check if subscription already exists
        subscription = Subscription.get_by_stripe_subscription_id(
            stripe_subscription.id
        )
        if subscription:
            logger.info("Subscription already exists: %s", stripe_subscription.id)
            return

        # Create subscription
        StripeController.create_subscription(stripe_subscription)

    @db_transaction()
    def handle_checkout_session_completed(event):
        checkout_session = event.data.object
        customer_id = checkout_session.customer
        user = User.query.filter_by(stripe_customer_id=customer_id).first()
        if not user:
            logger.error("No user found for customer %s", customer_id, exc_info=True)
            return

        logger.info("checkout_session: %s", checkout_session)
        logger.info("mode: %s", checkout_session.mode)

        # 处理一次性购买
        if checkout_session.mode == "payment":
            # 检查是否已处理过该支付
            existing_purchase = Purchase.get_by_stripe_payment(
                checkout_session.payment_intent
            )
            if existing_purchase:
                logger.info(
                    "Payment already processed: %s", checkout_session.payment_intent
                )
                return

            # 获取plan
            checkout_session = stripe.checkout.Session.retrieve(
                checkout_session.id,
                expand=["line_items"],
            )
            price_id = checkout_session.line_items.data[0].price.id
            plan = Plan.get_by_stripe_price_id(price_id)
            if not plan:
                logger.error("No plan found for price %s", price_id)
                return

            # 创建购买记录
            purchase = Purchase(
                id=id_generator.get_id(),
                user_id=user.id,
                plan_id=plan.id,
                quantity=checkout_session.line_items.data[0].quantity,
                stripe_payment_id=checkout_session.payment_intent,
            )
            db.session.add(purchase)
            db.session.flush()

            # 创建权益记录
            entitlement = EntitlementService.create_from_purchase(purchase)
            db.session.add(entitlement)

        # 订阅处理保持不变，由subscription.created事件处理
        logger.info("Checkout session completed for customer %s", customer_id)

    @db_transaction()
    def handle_subscription_updated(event):
        stripe_subscription = event.data.object
        subscription = Subscription.query.filter_by(
            stripe_subscription_id=stripe_subscription.id
        ).first()

        if not subscription:
            logger.info(
                "Subscription not found, creating new subscription: %s",
                stripe_subscription.id,
            )
            subscription = StripeController.create_subscription(stripe_subscription)
            if not subscription:
                logger.error(
                    "Failed to create subscription: %s",
                    stripe_subscription.id,
                    exc_info=True,
                )
                return

        # 更新订阅状态
        subscription.status = stripe_subscription.status
        subscription.current_period_start = convert_unixtime_to_datetime(
            stripe_subscription.current_period_start
        )
        subscription.current_period_end = convert_unixtime_to_datetime(
            stripe_subscription.current_period_end
        )
        subscription.cancel_at_period_end = stripe_subscription.cancel_at_period_end

        # 检查计划是否变更
        new_plan = Plan.get_by_stripe_price_id(stripe_subscription.plan.id)
        if new_plan:
            if subscription.plan_id != new_plan.id:
                subscription.plan_id = new_plan.id
        else:
            logger.error(
                "Plan not found for price %s",
                stripe_subscription.plan.id,
                exc_info=True,
            )

    @db_transaction()
    def handle_subscription_expired(event):
        stripe_subscription = event.data.object
        subscription = Subscription.get_by_stripe_subscription_id(
            stripe_subscription.id
        )
        if not subscription:
            logger.error(
                "Subscription not found: %s", stripe_subscription.id, exc_info=True
            )
            return

        current_time = datetime.now()

        # 1. 更新订阅状态
        subscription.status = SubscriptionStatus.EXPIRED.value
        subscription.current_period_end = current_time
        db.session.flush()

        # 处理权益
        EntitlementService.handle_subscription_expiration(
            subscription.id, subscription.user_id, termination_time=current_time
        )

    @db_transaction()
    def handle_invoice_paid(event):
        invoice = event.data.object
        subscription = Subscription.get_by_stripe_subscription_id(invoice.subscription)
        if not subscription:
            logger.error(
                "Subscription not found: %s", invoice.subscription, exc_info=True
            )
            return

        stripe_subscription = stripe.Subscription.retrieve(invoice.subscription)
        if stripe_subscription.status != "active":
            logger.error(
                "Subscription is not active: %s", invoice.subscription, exc_info=True
            )
            return

        # 处理权益
        EntitlementService.handle_subscription_payment(subscription)

    @staticmethod
    def validate_promo_code(promo_code, plan=None):
        """
        验证 promo code 的有效性，并返回 promotion code 的 ID

        Args:
            promo_code: 要验证的 promo code 值（如 "BASIC50"）
            plan: 可选的计划名称，用于验证 promo code 是否适用于该计划

        Returns:
            tuple: (是否有效, promotion code ID)
        """
        try:
            # 如果提供了 plan，检查是否有对应的映射
            if plan and plan in CONFIG.STRIPE_PROMO_CODE_LOOKUP_KEYS:
                expected_promo_code = CONFIG.STRIPE_PROMO_CODE_LOOKUP_KEYS[plan]
                if promo_code != expected_promo_code:
                    logger.error(
                        "Promo code %s does not match expected code %s for plan %s",
                        promo_code,
                        expected_promo_code,
                        plan,
                    )
                    return False, None

            # 直接调用 Stripe API 获取 promotion code 信息
            promotion_codes = stripe.PromotionCode.list(code=promo_code, active=True)
            if not promotion_codes.data:
                logger.error("No active promotion code found with code: %s", promo_code)
                return False, None

            # 获取第一个匹配的 promotion code
            promo_code_obj = promotion_codes.data[0]

            # 检查 promotion code 是否有效
            if not promo_code_obj.active:
                logger.error("Promotion code %s is not active", promo_code)
                return False, None

            # 检查是否有使用限制
            if (
                promo_code_obj.max_redemptions
                and promo_code_obj.times_redeemed >= promo_code_obj.max_redemptions
            ):
                logger.error(
                    "Promotion code %s has reached maximum redemptions", promo_code
                )
                return False, None

            # 检查是否过期
            if promo_code_obj.expires_at and promo_code_obj.expires_at < int(
                time.time()
            ):
                logger.error("Promotion code %s has expired", promo_code)
                return False, None

            return True, promo_code_obj.id
        except stripe.error.StripeError as e:
            logger.error("Error validating promo code: %s", str(e), exc_info=True)
            return False, None
