import logging

from flask_restful import abort
from flask import current_app
import sentry_sdk

from libs import id_generator
from models import db_transaction
from models.user import User
from libs.id_generator import id_generator
from controllers.email_validator import email_validator
from models.subscription import Subscription, SubscriptionStatus
from models.transcription_file import TranscriptionFile
from models.task import Task
from models.task_result import TaskResult
from models.share import Share
from models.user_preferences import UserPreferences
from services.entitlement_service import EntitlementService
from models import db
from exceptions import (
    BaseAPIException,
    UserNotFoundError,
    EntitlementCreationFailedError,
    UserAccountDeactivationFailedError,
)

logger = logging.getLogger(__name__)

# TODO: default avatar url and full name


@db_transaction()
def create_or_update_user(supabase_user):
    if not supabase_user:
        sentry_sdk.capture_message("Invalid user attempt")
        abort(401, message="invalid user")

    is_anonymous = supabase_user.get("is_anonymous")
    supabase_user_id = supabase_user["id"]
    if is_anonymous:
        email = ""
        full_name = ""
        first_name = ""
        last_name = ""
        avatar_url = ""
        provider = "anonymous"
    else:
        metadata = supabase_user["user_metadata"]

        # 获取 email，优先从顶层获取，如果没有则从 user_metadata 中获取
        email = supabase_user.get("email") or metadata.get("email")
        if not email:
            sentry_sdk.capture_message(
                "User without email",
                scope_kwargs={"supabase_user": supabase_user},
                level="error",
            )
            abort(401, message="invalid user: no email found")

        # Parse name information
        full_name = metadata.get("full_name") or ""
        first_name = metadata.get("first_name", "")
        last_name = metadata.get("last_name", "")
        avatar_url = metadata.get("avatar_url") or ""
        # If we have full_name but no first/last name (e.g., Google login)
        if full_name and not (first_name or last_name):
            name_parts = full_name.split(maxsplit=1)
            first_name = name_parts[0]
            last_name = name_parts[1] if len(name_parts) > 1 else ""

        # Get provider from supabase user metadata
        provider = supabase_user.get("app_metadata", {}).get("provider", "email")

    user = User.get_by_supabase_id(supabase_user_id)
    if user:
        user_id = user.id
        user.full_name = full_name
        user.first_name = first_name
        user.last_name = last_name
        user.avatar_url = avatar_url
        user.email = email
        user.provider = provider
        user.is_anonymous = is_anonymous
    else:
        # 仅仅在新注册用户时，验证邮箱域名，存量的允许
        if not is_anonymous and not email_validator.validate_email(email):
            abort(400, message="Disposable email addresses are not allowed")

        user = User(
            id=id_generator.get_id(),
            supabase_id=supabase_user_id,
            full_name=full_name,
            first_name=first_name,
            last_name=last_name,
            avatar_url=avatar_url,
            email=email,
            provider=provider,
            is_anonymous=is_anonymous,
        )
        user_id = User.create(user)

        # 初始化用户权益
        try:
            # 为新用户创建免费计划权益
            free_entitlement = EntitlementService.create_free_plan_entitlement(user_id)
            if free_entitlement:
                db.session.add(free_entitlement)
                logger.info(
                    f"Created initial free plan entitlement for new user {user_id}"
                )
            else:
                logger.error(
                    f"Failed to create initial free plan entitlement for user {user_id}"
                )
                raise EntitlementCreationFailedError(
                    "Failed to create initial free plan entitlement"
                )

            # 为新用户创建默认偏好设置
            preferences = UserPreferences(user_id=user_id)
            db.session.add(preferences)
            logger.info(f"Created default preferences for new user {user_id}")
        except Exception as e:
            logger.error(f"Failed to initialize user data: {str(e)}")
            raise

    return User.get_by_id(user_id)


@db_transaction()
def update_user(user, first_name=None, last_name=None):
    """
    更新用户基本信息

    Args:
        user (User): 用户对象
        first_name (str, optional): 用户名
        last_name (str, optional): 用户姓

    Returns:
        User: 更新后的用户对象
    """
    if first_name is not None:
        user.first_name = first_name
        # 如果修改了名字，也更新全名
        user.full_name = f"{first_name} {user.last_name}".strip()

    if last_name is not None:
        user.last_name = last_name
        # 如果修改了姓，也更新全名
        user.full_name = f"{user.first_name} {last_name}".strip()

    logger.info("Updated user information for user %s", user.id)
    return user


def _perform_account_deactivation(user_id, dry_run=True, storage=None, session=None):
    """
    执行账户注销的具体操作

    :param user_id: 用户ID
    :param dry_run: 是否为预览模式
    :param storage: 存储服务实例
    :param session: 数据库会话
    :return: 用户对象
    """
    # 1. 获取用户
    logger.info("Fetching user with ID %s", user_id)
    user = User.get_by_id(user_id)
    if not user:
        logger.error("User not found for ID %s", user_id)
        raise UserNotFoundError(f"User with ID {user_id} not found")

    # 2. 取消所有活跃订阅
    logger.info("Checking for active subscriptions for user %s", user_id)
    active_sub = Subscription.get_active_subscription_by_user_id(user_id)
    if active_sub:
        if active_sub.cancel_at_period_end:
            logger.info(
                "Active subscription for user %s is already set to cancel at period end",
                user_id,
            )
        else:
            # 报错，需要用户主动去 stripe 取消订阅
            raise UserAccountDeactivationFailedError(
                "Please cancel your subscription on the website before deactivating your account."
            )
        logger.info(
            (
                "Would cancel subscription for user %s"
                if dry_run
                else "Canceling active subscription for user %s"
            ),
            user_id,
        )

        if not dry_run:
            active_sub.status = SubscriptionStatus.CANCELED.value

    # 3. 删除转录文件及相关数据
    logger.info("Fetching transcription files for user %s", user_id)
    files = TranscriptionFile.get_by_user_id(user_id)
    for file in files:
        logger.info(
            (
                "Would delete tasks for file ID %s"
                if dry_run
                else "Deleting tasks for file ID %s"
            ),
            file.id,
        )
        # 删除任务
        tasks = Task.get_all_by_file_id(file.id)
        if not dry_run:
            for task in tasks:
                session.delete(task)

        logger.info(
            (
                "Would delete task results for file ID %s"
                if dry_run
                else "Deleting task results for file ID %s"
            ),
            file.id,
        )
        # 删除任务结果
        result = TaskResult.get_by_file_id(file.id)
        if result and not dry_run:
            session.delete(result)

        logger.info(
            (
                "Would delete shares for file ID %s"
                if dry_run
                else "Deleting shares for file ID %s"
            ),
            file.id,
        )
        # 删除分享
        share = Share.get_by_file_id(file.id)
        if share and not dry_run:
            session.delete(share)

        logger.info(
            (
                "Would mark file ID %s as deleted"
                if dry_run
                else "Marking file ID %s as deleted"
            ),
            file.id,
        )
        # 标记文件为已删除
        if not dry_run:
            file.is_deleted = True

        # 删除存储的文件
        if file.file_key:
            logger.info(
                (
                    "Would delete storage file for file ID %s, file_key: %s"
                    if dry_run
                    else "Deleting storage file for file ID %s, file_key: %s"
                ),
                file.id,
                file.file_key,
            )
            if not dry_run and storage:
                storage.permanently_delete_file(file.file_key)
        else:
            logger.warning("File key not found for file ID %s", file.id)

    # 4. 使用记录保留（usage/usage_history）

    # 5. 终止用户的所有权益
    logger.info(
        (
            "Would terminate all entitlements for user %s"
            if dry_run
            else "Terminating all entitlements for user %s"
        ),
        user_id,
    )
    if not dry_run:
        EntitlementService.terminate_all_user_entitlements(user_id)

    # 6. 标记用户为已注销
    logger.info(
        (
            "Would mark user %s as deactivated"
            if dry_run
            else "Marking user %s as deactivated"
        ),
        user_id,
    )

    if dry_run:
        logger.info("DRY RUN - Rolling back all changes")
        session.rollback()
    else:
        user.is_deactivated = True
        session.flush()

    return user


def deactivate_account(user_id, dry_run=True):
    """
    注销用户账号，删除相关数据

    :param user_id: 用户ID
    :param dry_run: 是否为预览模式，True时只显示将要执行的操作

    :raises UserNotFoundError: 如果用户不存在
    :raises UserAccountDeactivationFailedError: 如果注销过程中发生错误
    """
    logger.info("Deactivating account for user %s (dry_run=%s)", user_id, dry_run)
    try:
        storage = current_app.storage
    except AttributeError:
        logger.warning("Storage not available, file deletion will be skipped")
        storage = None

    try:
        with db_transaction() as session:
            # 执行账户注销的所有逻辑
            _perform_account_deactivation(
                user_id=user_id, dry_run=dry_run, storage=storage, session=session
            )

        return True
    except BaseAPIException as e:
        raise e
    except Exception as e:
        logger.error("Failed to deactivate account for user %s: %s", user_id, str(e))
        raise UserAccountDeactivationFailedError(
            f"Failed to deactivate account: {str(e)}"
        ) from e
