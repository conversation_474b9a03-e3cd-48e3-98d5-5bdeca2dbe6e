from datetime import datetime
import logging


from flask_restful import abort
from flask import current_app
from sqlalchemy.orm.attributes import flag_modified

from botocore.exceptions import ClientError, ConnectTimeoutError, ReadTimeoutError


from constants.language import LANGUAGES
from constants.storage import EXPIRE_FOR_READ_URL
from constants.task import TaskStatus, TaskType, TranscriptionType
from constants.transcription import (
    MAX_FILENAME_LENGTH,
    TranscriptionFileStatus,
    TranscriptionFileSourceType,
)
from models.user import User
from libs.id_generator import id_generator
from libs.timeout import (
    with_graceful_degradation,
    with_thread_timeout,
    TimeoutError as CustomTimeoutError,
)
from models import insert_record, db_transaction, db
from models.task import Task
from models.task_result import TaskResult
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from exceptions.storage import FileNotFoundError
from controllers.task import create_transcription_task
from services.entitlement_service import EntitlementService
from exceptions.transcription import InsufficientTranscriptionQuotaError
from exceptions.storage import FreeUserFileCountLimitExceededError

logger = logging.getLogger(__name__)


@db_transaction()
def create_transcription_file(
    user_id,
    filename,
    file_type,
    file_size,
    fingerprint,
    duration,
    language_code=None,
    transcription_type=TranscriptionType.TRANSCRIPT,
    create_storage_record=True,
    source_type=TranscriptionFileSourceType.UPLOAD,
    source_url=None,
):
    """创建转录文件，同时管理文件存储引用计数

    Args:
        user_id: 用户ID
        filename: 文件名
        file_type: 文件类型
        file_size: 文件大小
        fingerprint: 文件MD5指纹
        duration: 音频时长
        language_code: 语言代码
        transcription_type: 转录类型
        create_storage_record: 是否创建存储记录，默认为 True
        source_type: 文件来源类型，默认为 'upload'
        source_url: 媒体来源的URL，根据source_type不同而有不同含义

    Returns:
        TranscriptionFile: 创建的转录文件记录
    """
    # 文件类型交给前端判断，要不然需要同时维护两份文件类型列表
    file_type = file_type.lower()
    if language_code:
        language = LANGUAGES.get(language_code.lower(), None)
        if not language:
            abort(403, message="unsupported language")
    else:
        language = None

    now = datetime.now()
    id_ = id_generator.get_id()
    storage = current_app.storage
    key = storage.generate_key(user_id, fingerprint, file_type)
    # TODO: 废弃 file_url, 因为 file_url 需要动态生成
    file_url = storage.generate_file_url(key)

    # 检查文件长度并截断
    if len(filename) > MAX_FILENAME_LENGTH:
        filename = filename[:MAX_FILENAME_LENGTH]

    # 创建转录文件记录
    transcription_file = TranscriptionFile(
        id=id_,
        user_id=user_id,
        filename=filename,
        file_type=file_type,
        file_url=file_url,
        file_key=key,
        file_size=file_size,
        fingerprint=fingerprint,
        duration=duration,
        language_code=language_code,
        language=language,
        status=TranscriptionFileStatus.uploading.id,
        uploaded_time=None,
        created_time=now,
        updated_time=now,
        transcription_type=transcription_type,
        source_type=source_type,
        source_url=source_url,
    )
    insert_record(transcription_file)

    # 只有当 create_storage_record 为 True 且 fingerprint 不为空时才创建存储记录
    if create_storage_record and fingerprint:
        # 使用新的公共方法创建或更新存储记录
        FileStorage.create_or_update_storage(
            user_id, fingerprint, file_type, key, file_size
        )

    return TranscriptionFile.query.get(id_)


@db_transaction()
def complete_upload(user_id, transcription_file_id):
    start_time = datetime.now()
    # check if transcription file exists
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")
    logger.info(
        f"[file_id={transcription_file_id}] Transcription file existence check took: {datetime.now() - start_time}"
    )

    # check if user is allowed to complete upload
    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to complete upload this file")
    logger.info(
        f"[file_id={transcription_file_id}] User permission check took: {datetime.now() - start_time}"
    )

    # check if transcription file status is uploading
    if transcription_file.status != TranscriptionFileStatus.uploading.id:
        abort(403, message="Transcription file status is not uploading")
    logger.info(
        f"[file_id={transcription_file_id}] Transcription file status check took: {datetime.now() - start_time}"
    )

    # 检查文件是否存在
    storage = current_app.storage
    if not verify_file_integrity(
        storage, transcription_file, transcription_file.file_key
    ):
        abort(404, message="Transcription file not found in file storage")
    logger.info(
        f"[file_id={transcription_file_id}] File integrity verification took: {datetime.now() - start_time}"
    )

    transcription_file.status = TranscriptionFileStatus.uploaded.id
    transcription_file.uploaded_time = datetime.now()
    logger.info(
        f"[file_id={transcription_file_id}] Transcription file status update took: {datetime.now() - start_time}"
    )
    return transcription_file


# Note: 有其他地方会传入不同的 file_key, 所以需要传入 file_key
def verify_file_integrity(storage, transcription_file, file_key):
    """验证文件完整性

    Args:
        storage: 存储服务实例
        transcription_file: 转录文件对象
        file_key: 文件存储键

    Returns:
        bool: 文件完整性验证结果

    Note:
        此函数包含超时控制和降级策略，在接近 gunicorn 超时限制时会自动降级返回 True
    """

    # 实际的文件完整性验证逻辑
    def do_verify():
        expected_md5 = transcription_file.fingerprint
        expected_file_size = transcription_file.file_size

        start_time = datetime.now()
        logger.info(
            f"[file_id={transcription_file.id}] Starting file integrity check for key: {file_key}"
        )

        try:
            response = storage.client.head_object(
                Bucket=storage.config.get_bucket_name(), Key=file_key
            )

            elapsed = (datetime.now() - start_time).total_seconds()
            logger.info(
                f"[file_id={transcription_file.id}] File integrity check completed in {elapsed:.2f}s"
            )

            # 去掉 ETag 的引号再比较
            actual_md5 = response.get("ETag", "").strip('"')
            # 检查 ETag 是否为分段上传格式
            if "-" in actual_md5:
                # 处理分段上传的情况
                actual_size = response.get("ContentLength", 0)
                # 直接比较 file_size
                return expected_file_size == actual_size

            return actual_md5 == expected_md5

        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                raise FileNotFoundError(
                    message=f"File with ID {transcription_file.id} not found",
                    details={"file_id": transcription_file.id},
                )
            logger.exception(
                f"[file_id={transcription_file.id}] Error verifying file integrity: {e}"
            )
            raise  # Re-raise other exceptions
        except (ConnectTimeoutError, ReadTimeoutError) as e:
            logger.exception(
                f"[file_id={transcription_file.id}] Timeout error after all retries: {str(e)}"
            )
            raise
        except Exception as e:
            logger.exception(
                f"[file_id={transcription_file.id}] Unexpected error verifying file integrity: {e}"
            )
            raise

    # 从配置中获取超时时间和降级策略设置
    config = current_app.config.get("FILE_INTEGRITY_CHECK", {})
    timeout_seconds = config.get("timeout_seconds", 10)  # 默认10秒
    enable_degradation = config.get("enable_graceful_degradation", True)  # 默认启用降级

    # 如果启用了降级策略，使用带有优雅降级的超时控制
    if enable_degradation:
        return with_graceful_degradation(
            timeout_seconds,
            do_verify,
            fallback_value=True,
            log_prefix=f"[file_id={transcription_file.id}]",
        )
    else:
        # 不启用降级策略，任何错误都会抛出
        result, error = with_thread_timeout(timeout_seconds, do_verify)

        if error is None:
            return result
        elif isinstance(error, FileNotFoundError):
            # 文件确实不存在，重新抛出异常
            raise error
        elif isinstance(error, CustomTimeoutError):
            logger.error(
                f"[file_id={transcription_file.id}] File integrity check timed out after {timeout_seconds}s and degradation is disabled"
            )
            raise error
        else:
            logger.error(
                f"[file_id={transcription_file.id}] Error during file integrity check: {str(error)}"
            )
            raise error


def list_transcription_files(user_id, after_id, limit):
    status_list = [
        TranscriptionFileStatus.uploading.id,
        TranscriptionFileStatus.uploaded.id,
        TranscriptionFileStatus.processing.id,
        TranscriptionFileStatus.partially_completed.id,
        TranscriptionFileStatus.completed.id,
        TranscriptionFileStatus.failed.id,
        TranscriptionFileStatus.completed_with_errors.id,
    ]

    # 获取 limit + 1 条记录
    files = TranscriptionFile.list(user_id, after_id, limit + 1, status_list)

    has_more = len(files) > limit
    if has_more:
        next_cursor = files[limit - 1].id  # 使用第 limit 条记录的 id (去掉多查的那条)
        files = files[:-1]  # 去掉多查的那条记录
    else:
        next_cursor = None

    files = [format_transcription_file(file) for file in files]
    return {
        "items": files,
        "hasMore": has_more,
        "nextCursor": str(next_cursor) if next_cursor else None,
    }


def format_transcription_file(transcription_file):
    storage = current_app.storage
    transcription_file.file_url = storage.generate_presigned_url_for_read(
        transcription_file.file_key,
        EXPIRE_FOR_READ_URL,
    )
    transcription_file.status = TranscriptionFileStatus.by_id(
        transcription_file.status
    ).name
    return transcription_file


def get_transcription_file_with_result(user_id, transcription_file_id):
    from config import is_development_env

    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to view this file")

    # 需要用 把 file_url 替换为可以访问的 url
    transcription_file = format_transcription_file(transcription_file)

    task_result = TaskResult.get_by_file_id(transcription_file_id)
    if task_result:
        transcription_file.result = task_result
        if task_result.corrected_text:
            transcription_file.result.text = task_result.corrected_text
        else:
            transcription_file.result.text = task_result.original_text

    tasks = Task.get_all_by_file_id(transcription_file_id)
    task_statues = {}
    task_errors = {}
    transcription_time = 0  # 默认转录时间为0秒

    # 初始化为None，避免在非开发环境下返回空字符串
    transcription_file.requested_service_provider = None
    transcription_file.actual_service_provider = None

    for task in tasks:
        task_type_name = TaskType.by_id(task.task_type).name
        task_status = TaskStatus.by_id(task.status).name

        task_statues[task_type_name] = task_status
        task_errors[task_type_name] = task.error_message

        # 计算转录任务的耗时（秒）
        if (
            task.task_type == TaskType.transcription.id
            and task.status == TaskStatus.completed.id
        ):
            if task.started_time and task.completed_time:
                # 计算时间差并转换为秒，不足1秒按1秒计算
                time_diff = task.completed_time - task.started_time
                seconds = time_diff.total_seconds()
                transcription_time = max(1, int(seconds))  # 不足1秒按1秒计算

            # 如果是开发环境，添加请求和实际使用的服务提供商信息
            if is_development_env():
                transcription_file.requested_service_provider = (
                    task.requested_service_provider
                )
                transcription_file.actual_service_provider = (
                    task.actual_service_provider
                )

    transcription_file.task_statuses = task_statues
    transcription_file.task_errors = task_errors
    transcription_file.transcription_time = transcription_time
    return transcription_file


def simplify_segments(segments):
    """
    segments 里面的 tokens 字段不需要，删掉
    :param segments:
    :return:
    """
    new_segments = []
    for segment in segments:
        del segment["tokens"]
        new_segments.append(segment)
    return new_segments


@db_transaction()
def rename_transcription_file(user_id, transcription_file_id, new_filename):
    # Check if filename length is greater than MAX_FILENAME_LENGTH, if so, truncate it
    if len(new_filename) > MAX_FILENAME_LENGTH:
        new_filename = new_filename[:MAX_FILENAME_LENGTH]

    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to rename this file")

    # Skip update if the filename is the same
    if transcription_file.filename == new_filename:
        return transcription_file

    transcription_file.filename = new_filename
    transcription_file.updated_time = datetime.now()

    return transcription_file


@db_transaction()
def update_language_code(user_id, transcription_file_id, language_code):
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to update language code")

    language = LANGUAGES.get(language_code.lower(), None)
    if not language:
        abort(403, message="Unsupported language")

    transcription_file.language_code = language_code
    transcription_file.language = language
    transcription_file.updated_time = datetime.now()
    return transcription_file


@db_transaction()
def update_transcription_type(user_id, transcription_file_id, transcription_type):
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to update transcription type")

    transcription_file.transcription_type = transcription_type
    transcription_file.updated_time = datetime.now()
    return transcription_file


def list_transcription_files_by_page(
    user_id: int, page: int, page_size: int, offset: int
):
    query = TranscriptionFile.query.filter_by(user_id=user_id, is_deleted=False)

    # 获取总记录数
    total = query.count()

    # 获取当前页数据
    items = (
        query.order_by(TranscriptionFile.id.desc())
        .offset(offset)
        .limit(page_size)
        .all()
    )
    items = [format_transcription_file(item) for item in items]
    return {
        "items": items,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size,
    }


@db_transaction()
def migrate_anonymous_transcription_file(new_user, file_id):
    """迁移匿名用户的转录文件到新用户
    Args:
        new_user_id: 新用户ID
        file_id: 文件ID
    Returns:
        TranscriptionFile: 迁移后的文件
    """
    file = TranscriptionFile.get_by_id(file_id)
    if not file:
        abort(404, message="Transcription file not found")

    new_user_id = new_user.id
    if file.user_id == new_user_id:
        return file

    anonymous_user = User.get_by_id(file.user_id)
    if not anonymous_user:
        abort(404, message="Anonymous user not found")

    if not anonymous_user.is_anonymous:
        abort(403, message="User is not anonymous")

    # 检查当前用户的当日转录文件数量
    check_free_user_transcribe_limit(new_user)

    # 根据文件状态决定迁移方式
    if file.status not in [
        TranscriptionFileStatus.completed.id,
        TranscriptionFileStatus.partially_completed.id,
        TranscriptionFileStatus.completed_with_errors.id,
    ]:
        # 未完成的文件：简单迁移
        file.user_id = new_user_id
        file.updated_time = datetime.now()
        create_transcription_task(new_user_id, file_id)
        logger.info(f"Created transcription task for migrated file {file_id}")
    else:
        # 已完成的文件：完整迁移
        file = migrate_anonymous_user_data(anonymous_user.id, new_user_id, file_id)
        logger.info(f"Migrated all data for completed file {file_id}")

    return file


@db_transaction()
def migrate_anonymous_user_data(anonymous_user_id, target_user_id, file_id):
    """迁移匿名用户的最新文件数据到目标用户（完整迁移，用于已转录的文件）
    Args:
        anonymous_user_id: 匿名用户ID
        target_user_id: 目标用户ID
        file_id: 待迁移的文件ID
    """
    # 1. 验证用户身份
    anonymous_user = User.get_by_id(anonymous_user_id)
    if not anonymous_user or not anonymous_user.is_anonymous:
        abort(403, message="Source user must be anonymous")

    target_user = User.get_by_id(target_user_id)
    if not target_user or target_user.is_anonymous:
        abort(403, message="Target user must be regular user")

    # 2. 获取待迁移的文件
    file = TranscriptionFile.get_by_id(file_id)
    if not file or file.user_id != anonymous_user_id:
        abort(404, message="File not found or not owned by anonymous user")

    # 3. 迁移文件相关数据
    file.user_id = target_user_id
    file.updated_time = datetime.now()

    # Task 和 TaskResult 不用迁移，因为没有记录 user_id

    # 4. 更新目标用户权益使用量
    EntitlementService.update_usage(
        user_id=target_user_id, duration=file.duration, file_id=file.id
    )

    logger.info(
        f"Migrated file {file_id} from anonymous user {anonymous_user_id} to user {target_user_id}"
    )

    return file


@db_transaction()
def unlock_transcription(user_id, transcription_file_id):
    """
    解锁之前因额度不足而部分未处理的转录文件

    Args:
        user_id: 用户ID
        transcription_file_id: 转录文件ID

    Returns:
        dict: 成功解锁的文件信息
    """
    # 获取文件并验证所有权
    file = TranscriptionFile.get_by_id(transcription_file_id)
    if not file:
        abort(404, message="Transcription file not found")

    if file.user_id != user_id:
        abort(403, message="Permission denied")

    # 检查是否需要解锁
    if file.insufficient_minutes <= 0:
        return {"message": "File already unlocked", "file_id": transcription_file_id}

    minutes_to_unlock = file.insufficient_minutes

    # 从权益系统扣除额度
    # TODO: 后续文案从 minutes 改成 credits
    try:
        EntitlementService.unlock_file(user_id, minutes_to_unlock, file.id)
    except InsufficientTranscriptionQuotaError as e:
        # 直接重新抛出原始异常，保持403状态码
        raise
    except Exception as e:
        logger.error(f"Failed to update entitlement for unlock: {str(e)}")
        raise

    # 更新文件状态
    file.insufficient_minutes = 0

    return {
        "message": "File unlocked successfully",
        "file_id": transcription_file_id,
        "minutes_unlocked": minutes_to_unlock,
    }


def check_free_user_transcribe_limit(user):
    """
    检查用户的每日转录文件数量限制
    Args:
        user: 用户对象
    Raises:
        FreeUserFileCountLimitExceededError: 当用户超过每日转录限制时
    """
    today_upload_count = TranscriptionFile.get_user_today_transcribe_file_count(user.id)
    daily_limit = user.daily_transcribe_limit

    if today_upload_count >= daily_limit:
        if user.is_anonymous:
            message = f"Anonymous users can only transcribe {daily_limit} files per day. Please sign in to transcribe more files."
        else:
            message = f"Free users can only transcribe {daily_limit} files per day. Subscribe or purchase a one-time package to transcribe more files."

        raise FreeUserFileCountLimitExceededError(message=message)


@db_transaction()
def update_segment_text(user_id, transcription_file_id, segment_id, new_text):
    """更新单个segment的文本内容

    Args:
        user_id: 用户ID
        transcription_file_id: 转录文件ID
        segment_id: 要更新的segment ID
        new_text: 新的文本内容

    Returns:
        dict: 更新后的segment对象

    Raises:
        404: 如果文件或segment不存在
        403: 如果用户无权编辑该文件
    """
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to edit this file")

    task_result = TaskResult.get_by_file_id(transcription_file_id)
    if not task_result:
        abort(404, message="Task result not found")

    # 获取segments
    segments = task_result.segments

    # 查找并更新指定的segment
    segment_found = False
    for segment in segments:
        # 确保类型一致，segment_id可能是字符串
        segment_id_int = int(segment_id)
        if segment["id"] == segment_id_int:
            # 保存原始文本，可以用于日志记录
            original_text = segment["text"]
            logger.info(
                "Updating segment %s for file %s. Original text: '%s', New text: '%s'",
                segment_id,
                transcription_file_id,
                original_text,
                new_text,
            )

            # 更新文本
            segment["text"] = new_text
            segment_found = True

            # 添加更多日志，帮助调试
            logger.info(
                "Segment %s updated successfully. New segment data: %s",
                segment_id,
                segment,
            )
            break

    if not segment_found:
        abort(404, message=f"Segment with ID {segment_id} not found")

    # 将修改后的segments赋值回task_result
    task_result.segments = segments
    # 踩坑：使用flag_modified显式标记segments字段已修改。直接修改JSON字段不会触发 SQLAlchemy 的变化检测机制，因此需要手动标记。
    flag_modified(task_result, "segments")
    logger.info("Marked segments field as modified")

    # 返回更新后的segment
    for segment in segments:
        if segment["id"] == int(segment_id):
            return {
                "id": segment["id"],
                "start_time": segment["start_time"],
                "end_time": segment["end_time"],
                "text": segment["text"],
            }

    # 理论上不应该到达这里
    abort(500, message="Failed to retrieve updated segment")
