from datetime import datetime, timedelta
from typing import Optional
from models import db_transaction, db
from models.email_domain import EmailDomain
from services.usercheck_client import UsercheckClient
from config import CONFIG
import logging

logger = logging.getLogger(__name__)


class EmailValidatorService:
    def __init__(self):
        self.usercheck_client = UsercheckClient()
        self.cache_ttl = CONFIG.EMAIL_DOMAIN_CACHE_TTL

    def _extract_domain(self, email: str) -> str:
        return email.split("@")[1].lower()

    def _needs_update(self, domain_info: Optional[EmailDomain]) -> bool:
        if not domain_info:
            return True
        cache_ttl = timedelta(seconds=self.cache_ttl)
        return datetime.now() - domain_info.last_check_time > cache_ttl

    @db_transaction()
    def validate_email(self, email: str) -> bool:
        """
        验证邮箱是否可用于注册

        Args:
            email: 要验证的邮箱地址

        Returns:
            bool: True 表示邮箱可用于注册，False 表示邮箱不可用（一次性邮箱）

        Example:
            >>> validator = EmailValidatorService()
            >>> validator.validate_email("<EMAIL>")
            True  # 正常邮箱，可以注册
            >>> validator.validate_email("<EMAIL>")
            False  # 一次性邮箱，不可注册
        """
        domain = self._extract_domain(email)

        # 检查白名单
        if domain in CONFIG.WHITELIST_DOMAINS:
            return True

        # 查询本地数据库
        domain_info = EmailDomain.get_by_domain(domain)

        # 判断是否需要更新
        if self._needs_update(domain_info):
            try:
                result = self.usercheck_client.check_domain(domain)

                if domain_info:
                    # 更新现有记录
                    domain_info.is_disposable = result["is_disposable"]
                    domain_info.has_mx = result["has_mx"]
                    domain_info.is_public_domain = result["is_public_domain"]
                    domain_info.last_check_time = datetime.now()
                else:
                    # 创建新记录
                    domain_info = EmailDomain(
                        domain=domain,
                        is_disposable=result["is_disposable"],
                        has_mx=result["has_mx"],
                        is_public_domain=result["is_public_domain"],
                        last_check_time=datetime.now(),
                    )
                    db.session.add(domain_info)

                logger.info(f"Domain check result for {domain}: {result}")
                return not result["is_disposable"]

            except Exception as e:
                logger.error(f"Error checking domain {domain}: {str(e)}")
                # API 异常时,如果有缓存数据则使用缓存,否则默认允许注册
                if domain_info:
                    return not domain_info.is_disposable
                return True

        return not domain_info.is_disposable


email_validator = EmailValidatorService()
