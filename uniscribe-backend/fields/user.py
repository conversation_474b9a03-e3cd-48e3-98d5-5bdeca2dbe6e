from flask_restful import fields
from fields.user_preferences import user_preferences_fields

# 计划详细信息字段
plan_detail_fields = {
    "tier": fields.String(attribute="tier"),
    "planType": fields.String(attribute="plan_type"),
    "name": fields.String(attribute="name"),
    "creditAmount": fields.Integer(attribute="credit_amount"),
    "isAppsumo": fields.Boolean(attribute="is_appsumo"),
}

user_fields = {
    "id": fields.String(attribute="id"),
    "supabaseId": fields.String(attribute="supabase_id"),
    "email": fields.String(attribute="email"),
    "avatarUrl": fields.String(attribute="avatar_url"),
    "fullName": fields.String(attribute="full_name"),
    "firstName": fields.String(attribute="first_name"),
    "lastName": fields.String(attribute="last_name"),
    "isDeactivated": fields.Boolean(attribute="is_deactivated"),
    "stripeCustomerId": fields.String(attribute="stripe_customer_id"),
    "createdTime": fields.String(attribute="created_time"),
    "updatedTime": fields.String(attribute="updated_time"),
    "hasActiveSubscription": fields.Boolean(
        attribute="has_active_subscription"
    ),  # 保持向后兼容
    "hasPaidPlan": fields.Boolean(attribute="has_paid_plan"),  # 新增字段，更准确的名称
    "activePlan": fields.String(attribute="active_plan"),  # 保持向后兼容
    "activePlans": fields.List(
        fields.String, attribute="active_plans"
    ),  # 新增字段，返回所有活跃计划
    "primaryPlan": fields.String(attribute="primary_plan"),  # 新增字段，返回主要计划
    "primaryPlanDetail": fields.Nested(
        plan_detail_fields, attribute="primary_plan_detail"
    ),  # 新增字段，返回主要计划的详细信息
    "activePlansDetail": fields.List(
        fields.Nested(plan_detail_fields), attribute="active_plans_detail"
    ),  # 新增字段，返回所有活跃计划的详细信息
    "displayName": fields.String(attribute="display_name"),
    "provider": fields.String(attribute="provider"),
    "isAnonymous": fields.Boolean(attribute="is_anonymous"),
    "preferences": fields.Nested(user_preferences_fields, attribute="preferences"),
}

email_check_fields = {
    "email": fields.String(attribute="email"),
    "isDeactivated": fields.Boolean(attribute="is_deactivated"),
    "isDisposable": fields.Boolean(attribute="is_disposable"),
    "isRegistered": fields.Boolean(attribute="is_registered"),
}
