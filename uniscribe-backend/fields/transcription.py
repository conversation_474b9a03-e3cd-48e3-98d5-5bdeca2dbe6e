from flask_restful import fields
from fields.custom_fields import ISO8601DateTime


transcription_segment_fields = {
    "id": fields.String(attribute="id"),
    "start": fields.String(attribute="start_time"),
    "end": fields.String(attribute="end_time"),
    "text": fields.String(attribute="text"),
}

qa_pair_fields = {
    "question": fields.String(attribute="question"),
    "answer": fields.String(attribute="answer"),
}

task_result_fields = {
    "id": fields.String(attribute="id"),
    "language": fields.String(attribute="language"),
    "text": fields.String(attribute="text"),
    "segments": fields.List(
        fields.Nested(transcription_segment_fields), attribute="segments"
    ),
    "summary": fields.String(attribute="summary"),
    "outline": fields.String(attribute="outline"),
    "qaExtraction": fields.List(
        fields.Nested(qa_pair_fields), attribute="qa_extraction"
    ),
}


task_statuses_fields = {
    "transcription": fields.String(attribute="transcription"),
    "summary": fields.String(attribute="summary"),
    "outline": fields.String(attribute="outline"),
    "qaExtraction": fields.String(attribute="qa_extraction"),
    "youtubeDownload": fields.String(attribute="youtube_download"),
}

task_errors_fields = {
    "transcription": fields.String(attribute="transcription"),
    "summary": fields.String(attribute="summary"),
    "outline": fields.String(attribute="outline"),
    "qaExtraction": fields.String(attribute="qa_extraction"),
    "youtubeDownload": fields.String(attribute="youtube_download"),
}

transcription_file_fields = {
    "id": fields.String(attribute="id"),
    "filename": fields.String(attribute="filename"),
    "fileType": fields.String(attribute="file_type"),
    "fileUrl": fields.String(attribute="file_url"),
    "fileSize": fields.Integer(attribute="file_size"),
    "status": fields.String(attribute="status"),
    "fingerprint": fields.String(attribute="finger_print"),
    "duration": fields.Float(attribute="duration"),
    "uploadedTime": ISO8601DateTime(attribute="uploaded_time"),
    "createdTime": ISO8601DateTime(attribute="created_time"),
    "updatedTime": ISO8601DateTime(attribute="updated_time"),
    "result": fields.Nested(task_result_fields),
    "taskStatuses": fields.Nested(task_statuses_fields, attribute="task_statuses"),
    "taskErrors": fields.Nested(task_errors_fields, attribute="task_errors"),
    "languageCode": fields.String(attribute="language_code"),
    "transcriptionType": fields.String(attribute="transcription_type"),
    "insufficientMinutes": fields.Integer(attribute="insufficient_minutes"),
    "transcriptionTime": fields.Integer(attribute="transcription_time"),
    "sourceType": fields.String(attribute="source_type"),
    "sourceUrl": fields.String(attribute="source_url"),
    "mediaDownloadTime": fields.Integer(attribute="media_download_time"),
    "requestedServiceProvider": fields.String(attribute="requested_service_provider"),
    "actualServiceProvider": fields.String(attribute="actual_service_provider"),
}


pagination_fields = {
    "items": fields.List(fields.Nested(transcription_file_fields)),
    "hasMore": fields.Boolean,
    "nextCursor": fields.String,
}

page_based_pagination_fields = {
    "items": fields.List(fields.Nested(transcription_file_fields)),
    "total": fields.Integer,
    "page": fields.Integer,
    "pageSize": fields.Integer(attribute="page_size"),
    "totalPages": fields.Integer(attribute="total_pages"),
}
