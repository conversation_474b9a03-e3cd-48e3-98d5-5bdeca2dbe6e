from flask_restful import fields
from datetime import datetime


class ISO8601DateTime(fields.Raw):
    """
    自定义字段类，将 datetime 对象格式化为 ISO 8601 格式的字符串
    
    ISO 8601 格式示例: 2023-04-15T08:30:45.123Z
    """
    def format(self, value):
        if value is None:
            return None
        
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            except (ValueError, TypeError):
                return value
                
        if isinstance(value, datetime):
            # 转换为 ISO 8601 格式，包含毫秒并使用 Z 表示 UTC
            return value.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
            
        return str(value)
