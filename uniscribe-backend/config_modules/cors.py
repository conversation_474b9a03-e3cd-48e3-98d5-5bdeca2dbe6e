"""CORS 配置模块"""

from flask_cors import CORS
from config import is_development_env


def setup_cors(app):
    """设置 CORS 配置"""
    if is_development_env():
        CORS(app, supports_credentials=True, allow_headers="*", origins="*")
    else:
        CORS(
            app,
            origins=[
                "https://shiyin-web.vercel.app",
                "https://www.uniscribe.co",
                "https://uniscribe.co",
                r"^https:\/\/uniscribe-.*\.vercel\.app$",
            ],
            supports_credentials=True,
        )
