from enum import Enum
from libs.typed_enum import TypedEnum


class TaskStatus(TypedEnum):
    # TODO: 改成大写
    pending = 1  # 任务已创建，等待执行
    processing = 2  # 处理中
    completed = 3  # 处理完成
    failed = 4  # 处理失败


class TaskType(TypedEnum):
    """
    任务的依赖关系：
    1. 转录任务依赖于音频文件
    2. 校对任务依赖于转录任务，校对完成后，转录文件的状态变为
    3. 其他任务依赖于校对任务，这些任务可以并行执行

    其中 转录任务和校对任务是关键任务，其他任务是非关键任务。
    """

    # TODO: 改成大写

    transcription = 1  # 转录任务
    correction = 2  # 校对任务
    summary = 3  # 摘要任务
    keywords = 4  # 关键词任务
    outline = 5  # 大纲任务
    qa_extraction = 6  # 问答任务
    translation = 7  # 翻译任务
    youtube_download = 8  # YouTube 下载任务


class TaskFeature(TypedEnum):
    text_summary = 1  # 文本摘要任务
    mind_map = 2  # 思维导图任务
    qa_extraction = 3  # 问答任务


class TranscriptionType(str, Enum):
    TRANSCRIPT = "transcript"  # 转录文本
    SUBTITLE = "subtitle"  # 字幕文本
