from enum import Enum
from libs.typed_enum import TypedEnum


MAX_AUDIO_FILE_SIZE = 1024 * 1024 * 1024  # 1GB

# 免费用户每天限制文件数量
FREE_USER_MAX_FILE_COUNT_PER_DAY = 5
ANONYMOUS_USER_MAX_FILE_COUNT_PER_DAY = 2
MAX_FILENAME_LENGTH = 100


class TranscriptionFileSourceType(str, Enum):
    """转录文件来源类型"""

    UPLOAD = "upload"  # 用户直接上传的文件
    YOUTUBE = "youtube"  # 从 YouTube 下载的文件
    # 未来可能的其他来源
    # SPOTIFY = "spotify"
    # PODCAST = "podcast"
    # 等等


class TranscriptionFileStatus(TypedEnum):
    # TODO: 改成大写
    uploading = 1  # 上传中
    uploaded = 2  # 上传成功，但未支付。 所有任务尚未开始处理
    processing = 3  # 处理中， 至少有一个任务正在处理（此时没有任何任务完成）
    partially_completed = 4  # 部分任务完成。 所有任务中，有一部分任务已成功完成（可能有部分非关键任务任务失败），用户可以查看部分处理结果。允许对部分任务进行重试。
    completed = 5  # 全部任务完成。 所有任务都已成功完成，用户可以查看全部处理结果。
    failed = 6  # 文件处理失败，且所有任务都失败或部分关键任务失败，导致无法继续处理。可以重试。
    completed_with_errors = 7  # 所有任务都已处于最终状态，但有部分非关键任务失败。用户可以查看部分处理结果，不需要继续轮询。
