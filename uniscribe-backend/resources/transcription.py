from venv import logger
from flask import g
from flask_restful import Resource, marshal_with, abort, marshal

from constants.common import DEFAULT_LIMIT, MAX_LIMIT
from constants.transcription import TranscriptionFileStatus
from controllers.transcription import (
    format_transcription_file,
    list_transcription_files,
    get_transcription_file_with_result,
    migrate_anonymous_transcription_file,
    rename_transcription_file,
    update_language_code,
    list_transcription_files_by_page,
    update_transcription_type,
    unlock_transcription,
    update_segment_text,
)
from fields.transcription import (
    transcription_file_fields,
    pagination_fields,
    page_based_pagination_fields,
)
from libs import reqparse
from libs.id_generator import id_generator
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from models import db
from resources.auth import auth_required


class TranscriptionsResource(Resource):
    @auth_required
    @marshal_with(pagination_fields)
    def get(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        after_id = parser.get_argument(
            "cursor", type=int, location="args", default=id_generator.get_id()
        )
        limit = parser.get_argument(
            "limit", type=int, location="args", default=DEFAULT_LIMIT
        )

        # 限制 limit 的范围
        limit = min(max(1, limit), MAX_LIMIT)

        return list_transcription_files(user_id, after_id, limit)


class TranscriptionResource(Resource):
    @auth_required
    @marshal_with(transcription_file_fields)
    def get(self, transcription_file_id):
        user_id = g.user.id
        transcription_file_with_result = get_transcription_file_with_result(
            user_id, transcription_file_id
        )
        return transcription_file_with_result

    @auth_required
    @marshal_with(transcription_file_fields)
    def patch(self, transcription_file_id):
        user_id = g.user.id

        # Parse the request body
        parser = reqparse.RequestParser()
        parser.add_argument(
            "filename", type=str, required=False, help="New filename is required"
        )
        parser.add_argument(
            "languageCode",
            type=str,
            required=False,
            help="New language code is required",
        )
        parser.add_argument(
            "transcriptionType",
            type=str,
            required=False,
            help="New transcription type is required",
        )
        args = parser.parse_args()

        tf = None
        if args.languageCode:
            tf = update_language_code(user_id, transcription_file_id, args.languageCode)
        if args.filename:
            tf = rename_transcription_file(
                user_id, transcription_file_id, args.filename
            )
        if args.transcriptionType:
            tf = update_transcription_type(
                user_id, transcription_file_id, args.transcriptionType
            )
        return tf


class TranscriptionStatusResource(Resource):
    @auth_required
    def get(self, transcription_file_id):
        user_id = g.user.id
        transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
        if not transcription_file:
            abort(404, message="Transcription file not found")
        return {"status": TranscriptionFileStatus.by_id(transcription_file.status).name}


class DeleteTranscriptionResource(Resource):

    allow_deleted_status = [
        TranscriptionFileStatus.uploading.id,
        TranscriptionFileStatus.uploaded.id,
        TranscriptionFileStatus.completed.id,
        TranscriptionFileStatus.failed.id,
        TranscriptionFileStatus.completed_with_errors.id,
    ]

    @auth_required
    def delete(self, transcription_file_id):
        user_id = g.user.id

        # 获取文件并验证所有权
        file = TranscriptionFile.get_by_id(transcription_file_id)
        if not file:
            abort(404, message="File not found")

        if file.user_id != user_id:
            abort(403, message="Unauthorized")

        # 检查文件状态是否允许删除
        if file.status not in self.allow_deleted_status:
            abort(403, message="File status is not allowed to delete")

        # 减少文件存储引用计数
        if file.fingerprint:
            FileStorage.decrement_reference(file.user_id, file.fingerprint)

        # 软删除文件
        file.is_deleted = True
        db.session.commit()

        return {"message": "File deleted successfully"}, 200


class TranscriptionsByPageResource(Resource):
    @auth_required
    @marshal_with(page_based_pagination_fields)
    def get(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        page = parser.get_argument("page", type=int, location="args", default=1)
        page_size = parser.get_argument(
            "pageSize", type=int, location="args", default=DEFAULT_LIMIT
        )

        # 限制每页数量范围
        page_size = min(max(1, page_size), MAX_LIMIT)
        # 确保页码为正数
        page = max(1, page)

        offset = (page - 1) * page_size
        return list_transcription_files_by_page(user_id, page, page_size, offset)


class TranscriptionSearchResource(Resource):
    @auth_required
    @marshal_with(page_based_pagination_fields)
    def get(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        parser.add_argument(
            "keyword",
            type=str,
            required=True,
            location="args",
            help="Search keyword is required",
        )
        parser.add_argument("page", type=int, location="args", default=1)
        parser.add_argument(
            "pageSize", type=int, location="args", default=DEFAULT_LIMIT
        )
        args = parser.parse_args()

        # 限制每页数量范围
        page_size = min(max(1, args.pageSize), MAX_LIMIT)
        # 确保页码为正数
        page = max(1, args.page)

        # 计算偏移量
        offset = (page - 1) * page_size

        # 获取总数和分页数据
        total = TranscriptionFile.query.filter(
            TranscriptionFile.user_id == user_id,
            TranscriptionFile.filename.like(f"%{args.keyword}%"),
            TranscriptionFile.is_deleted == False,
        ).count()

        items = (
            TranscriptionFile.query.filter(
                TranscriptionFile.user_id == user_id,
                TranscriptionFile.filename.like(f"%{args.keyword}%"),
                TranscriptionFile.is_deleted == False,
            )
            .order_by(TranscriptionFile.created_time.desc())
            .offset(offset)
            .limit(page_size)
            .all()
        )
        items = [format_transcription_file(item) for item in items]
        total_pages = (total + page_size - 1) // page_size
        return {
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
        }


class AnonymousLatestTranscriptionResource(Resource):
    @auth_required
    def get(self):
        user = g.user
        if not user.is_anonymous:
            abort(403, message="This endpoint is only available for anonymous users")

        # 获取该匿名用户最新的一个文件
        latest_file = (
            TranscriptionFile.query.filter(
                TranscriptionFile.user_id == user.id,
                TranscriptionFile.is_deleted == False,
            )
            .order_by(TranscriptionFile.created_time.desc())
            .first()
        )
        if not latest_file:
            # FIXME: 如果用 marshal_with, 会导致这里也被 marshal，而不是返回 None
            return None

        return marshal(
            format_transcription_file(latest_file), transcription_file_fields
        )


# 迁移匿名用户的文件到新的登录用户
class MigrateAnonymousTranscriptionResource(Resource):
    @auth_required
    def post(self):
        user = g.user
        if user.is_anonymous:
            abort(403, message="This endpoint is for regular user")

        parser = reqparse.RequestParser()
        file_id = parser.get_argument(
            "fileId", type=int, required=True, location="json"
        )

        migrate_anonymous_transcription_file(user, file_id)
        return {
            "message": "File migrated and transcription task created successfully",
        }


class UnlockTranscriptionResource(Resource):
    @auth_required
    def post(self, transcription_file_id):
        """解锁因配额不足而未完全处理的转录文件"""
        user_id = g.user.id
        return unlock_transcription(user_id, transcription_file_id)


class TranscriptionSegmentResource(Resource):
    @auth_required
    def patch(self, transcription_file_id, segment_id):
        """更新单个segment的文本内容"""
        user_id = g.user.id
        parser = reqparse.RequestParser()
        text = parser.get_argument("text", type=str, required=True, location="json")

        # 更新segment
        updated_segment = update_segment_text(
            user_id, transcription_file_id, segment_id, text
        )

        # 确保返回的字段名称与前端期望的一致
        return {
            "success": True,
            "segment": {
                "id": str(updated_segment["id"]),
                "start": str(updated_segment["start_time"]),
                "end": str(updated_segment["end_time"]),
                "text": updated_segment["text"],
            },
        }
