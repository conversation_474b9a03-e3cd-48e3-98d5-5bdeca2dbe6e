import logging

from flask_restful import Resource, abort
from werkzeug.exceptions import HTTPException

from libs import reqparse
from controllers.export import export_text
from models.share import Share
from resources.auth import auth_required

logger = logging.getLogger(__name__)


class ExportResource(Resource):
    allowed_file_types = ["pdf", "docx", "txt", "srt", "vtt", "csv"]

    @auth_required
    def post(self):
        parser = reqparse.RequestParser()
        file_type = parser.get_argument(
            "fileType", type=str, required=True, location="json"
        )
        file_id = parser.get_argument(
            "fileId", type=str, required=True, location="json"
        )
        show_speaker_name = parser.get_argument(
            "showSpeakerName", type=bool, required=False, location="json", default=True
        )
        show_timestamps = parser.get_argument(
            "showTimestamps", type=bool, required=False, location="json", default=True
        )
        if file_type not in self.allowed_file_types:
            abort(400, message="Invalid file type")

        # 打印参数
        logger.info(
            f"file_type: {file_type}, file_id: {file_id}, show_speaker_name: {show_speaker_name}, show_timestamps: {show_timestamps}"
        )

        try:
            return export_text(
                file_type,
                file_id,
                show_speaker_name if show_speaker_name is not None else True,
                show_timestamps if show_timestamps is not None else True,
            )
        except NotImplementedError as e:
            abort(400, message=str(e))
        except HTTPException as e:
            raise
        except Exception as e:
            logger.exception("An error occurred during file export")
            abort(500, message="An error occurred during file export")


class ExportShareResource(Resource):
    allowed_file_types = ["pdf", "docx", "txt", "srt", "vtt", "csv"]

    # no auth required
    def post(self):
        parser = reqparse.RequestParser()
        file_type = parser.get_argument(
            "fileType", type=str, required=True, location="json"
        )
        file_id = parser.get_argument(
            "fileId", type=str, required=True, location="json"
        )
        show_speaker_name = parser.get_argument(
            "showSpeakerName", type=bool, required=False, location="json", default=True
        )
        show_timestamps = parser.get_argument(
            "showTimestamps", type=bool, required=False, location="json", default=True
        )
        if file_type not in self.allowed_file_types:
            abort(400, message="Invalid file type")

        # check file shared
        share = Share.get_by_file_id(file_id)
        if not share:
            abort(404, message="File not shared")

        try:
            return export_text(
                file_type,
                file_id,
                show_speaker_name if show_speaker_name is not None else True,
                show_timestamps if show_timestamps is not None else True,
            )
        except NotImplementedError as e:
            abort(400, message=str(e))
        except HTTPException as e:
            raise
        except Exception as e:
            logger.exception("An error occurred during file export")
            abort(500, message="An error occurred during file export")
