from flask_restful import Resource, reqparse
from flask import g
from controllers.appsumo_webhook import AppSumoWebhookController
from controllers.appsumo_oauth import AppSumoOAuthController
from resources.auth import auth_required


class AppSumoWebhookResource(Resource):
    def post(self):
        """处理 AppSumo webhook"""
        return AppSumoWebhookController.handle_webhook()


class AppSumoActivateResource(Resource):
    def __init__(self):
        self.parser = reqparse.RequestParser()
        self.parser.add_argument("code", type=str, required=True, location="json")
        super().__init__()

    @auth_required
    def post(self):
        """为已登录用户激活 AppSumo license"""
        args = self.parser.parse_args()
        user_id = g.user.id
        return AppSumoOAuthController.activate_license(user_id, args["code"])
