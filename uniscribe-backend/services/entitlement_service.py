import logging
import math
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from constants.subscription import SubscriptionStatus
from models.plan import Plan, PlanType
from models.purchase import Purchase
from models.entitlement import Entitlement, EntitlementSource
from models.subscription import Subscription
from models import db
from libs.id_generator import id_generator
from exceptions.transcription import InsufficientTranscriptionQuotaError
from models.credit_usage import CreditUsage
from models.transcription_file import TranscriptionFile

logger = logging.getLogger(__name__)


class EntitlementService:
    @staticmethod
    def create_from_purchase(purchase: Purchase):
        """处理一次性购买生成权益"""
        # 检查是否已存在权益
        existing = Entitlement.query.filter_by(
            source_type=EntitlementSource.ONE_TIME, source_id=purchase.id
        ).first()

        if existing:
            logger.info("Entitlement already exists for purchase %s", purchase.id)
            return existing

        plan = Plan.query.get(purchase.plan_id)

        reference_time = datetime.now()
        valid_from = reference_time
        valid_until = reference_time + timedelta(days=plan.validity_days)

        return Entitlement(
            id=id_generator.get_id(),
            user_id=purchase.user_id,
            total_credits=plan.credit_amount * purchase.quantity,
            consumed_credits=0,
            valid_from=valid_from,
            valid_until=valid_until,
            source_type=EntitlementSource.ONE_TIME,
            source_id=purchase.id,
            is_recurring=False,
        )

    @staticmethod
    def create_from_ltd_purchase(purchase: Purchase, reference_time=None):
        """处理 LTD 计划购买生成权益

        LTD 计划的特点：
        1. 一次性付费，终身有效
        2. 每月重置积分
        3. 使用专门的 EntitlementSource.LTD 类型

        Args:
            purchase: 购买记录
            reference_time: 参考时间点，用于计算有效期。如果不提供，默认使用当前时间
        """
        if reference_time is None:
            reference_time = datetime.now()

        plan = Plan.query.get(purchase.plan_id)

        # 在初次创建时，检查是否已存在有效的权益
        # 在周期性重置时，我们会传入不同的 reference_time
        # 查找有效的 LTD 权益
        existing = Entitlement.query.filter(
            Entitlement.source_type == EntitlementSource.LTD,
            Entitlement.source_id == purchase.id,
            Entitlement.is_recurring == True,
            Entitlement.valid_until > reference_time,  # 只检查有效的权益
        ).first()

        if existing:
            logger.info(
                "Valid LTD entitlement already exists for purchase %s", purchase.id
            )
            return existing

        # 设置有效期：从参考时间到下个月
        valid_from = reference_time
        valid_until = reference_time + relativedelta(months=1)

        return Entitlement(
            id=id_generator.get_id(),
            user_id=purchase.user_id,
            total_credits=plan.credit_amount * purchase.quantity,
            consumed_credits=0,
            valid_from=valid_from,
            valid_until=valid_until,
            source_type=EntitlementSource.LTD,
            source_id=purchase.id,
            is_recurring=True,  # 设置为周期性重置
        )

    @staticmethod
    def create_from_subscription(subscription, reference_time=None):
        """处理订阅生成权益（每月/每年重置）
        Args:
            subscription: 订阅对象
            reference_time: 参考时间点，用于计算有效期。如果不提供，默认使用当前时间
        """
        plan = Plan.query.get(subscription.plan_id)
        if reference_time is None:
            reference_time = datetime.now()

        # 根据订阅类型设置有效期
        if plan.interval.lower() == "year":
            # 年订阅：按月重置
            valid_from = reference_time
            valid_until = reference_time + relativedelta(months=1)
        else:
            # 月订阅：使用订阅周期
            valid_from = subscription.current_period_start
            valid_until = subscription.current_period_end

        return Entitlement(
            id=id_generator.get_id(),
            user_id=subscription.user_id,
            total_credits=plan.credit_amount,
            consumed_credits=0,
            valid_from=valid_from,
            valid_until=valid_until,
            source_type=EntitlementSource.SUBSCRIPTION,
            source_id=subscription.id,
            is_recurring=True,
        )

    @staticmethod
    def create_free_plan_entitlement(user_id, reference_time=None):
        """创建免费计划权益
        Args:
            user_id: 用户ID
            reference_time: 参考时间点，用于计算有效期。如果不提供，默认使用当前时间
        """
        free_plan = Plan.query.filter_by(
            plan_type=PlanType.FREE, interval="month"
        ).first()

        if not free_plan:
            logger.error("Free plan not found in database")
            return None

        if reference_time is None:
            reference_time = datetime.now()

        return Entitlement(
            id=id_generator.get_id(),
            user_id=user_id,
            total_credits=free_plan.credit_amount,
            consumed_credits=0,
            valid_from=reference_time,
            valid_until=reference_time + relativedelta(months=1),
            source_type=EntitlementSource.FREE_PLAN,
            source_id=user_id,
            is_recurring=True,
        )

    @staticmethod
    def terminate_subscription_entitlements(subscription_id, termination_time=None):
        """终止订阅相关的所有权益"""
        if termination_time is None:
            termination_time = datetime.now()

        Entitlement.query.filter_by(
            source_type=EntitlementSource.SUBSCRIPTION,
            source_id=subscription_id,
            is_recurring=True,
        ).update(
            {
                "is_recurring": False,
                "valid_until": termination_time,
                "updated_time": termination_time,
            }
        )

    @staticmethod
    def terminate_ltd_entitlements(purchase_id, termination_time=None):
        """终止 LTD 计划相关的所有权益

        Args:
            purchase_id: 购买记录ID
            termination_time: 终止时间，默认为当前时间
        """
        if termination_time is None:
            termination_time = datetime.now()

        # 查找与购买记录关联的所有 LTD 权益
        entitlements = Entitlement.query.filter_by(
            source_type=EntitlementSource.LTD,
            source_id=purchase_id,
        ).all()

        terminated_count = 0
        for entitlement in entitlements:
            entitlement.is_recurring = False
            entitlement.valid_until = termination_time
            entitlement.updated_time = termination_time
            terminated_count += 1

        logger.info(
            "Terminated %s LTD entitlements for purchase_id=%s",
            terminated_count,
            purchase_id,
        )

        return terminated_count

    @staticmethod
    def update_usage(user_id, duration, file_id):
        """
        更新使用量
        Args:
            user_id: 用户ID
            duration: 音频时长（秒）
            file_id: 转录文件ID
        """
        try:
            minutes_needed = math.ceil(duration / 60)

            # 获取总可用额度
            credits = EntitlementService.get_user_credits(user_id)
            remaining_credits = credits["remaining_credits"]

            # 更新文件状态
            file = TranscriptionFile.get_by_id(file_id)
            if remaining_credits < minutes_needed:
                file.insufficient_minutes = minutes_needed - remaining_credits
                # 只使用剩余可用额度
                minutes_to_consume = remaining_credits
            else:
                minutes_to_consume = minutes_needed

            # 使用共享方法消费积分
            if minutes_to_consume > 0:
                EntitlementService._consume_credits(
                    user_id, minutes_to_consume, file_id, service_type="transcription"
                )

            db.session.commit()

        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to update usage for user {user_id}: {str(e)}")
            raise

    @staticmethod
    def check_quota(user_id, duration):
        """检查配额是否足够"""
        credits = EntitlementService.get_user_credits(user_id)
        remaining_credits = credits["remaining_credits"]
        minutes_needed = math.ceil(duration / 60)
        # TODO：支持其他消费场景，比如翻译、说话人识别、字幕等。兑换比例可能不是 1:1

        # 只在完全没有剩余额度时阻止转录
        if remaining_credits <= 0:
            raise InsufficientTranscriptionQuotaError(
                "You have used up all your transcription minutes. Please upgrade your plan to continue."
            )

        # 如果剩余额度不足但大于0，记录日志但允许继续
        if remaining_credits < minutes_needed:
            logger.info(
                f"User {user_id} is transcribing with insufficient entitlement credits: "
                f"remaining={remaining_credits:.2f} minutes, requested={minutes_needed:.2f} minutes"
            )

    @staticmethod
    def get_user_credits(user_id):
        """获取用户的额度情况
        Returns:
            dict: {
                "total_credits": 总额度,
                "remaining_credits": 可用额度
            }
        """
        # 获取所有有效权益的总额度和已用额度
        active_entitlements = Entitlement.get_active_entitlements(user_id)
        total_credits = sum(
            entitlement.total_credits for entitlement in active_entitlements
        )
        consumed_credits = sum(
            entitlement.consumed_credits for entitlement in active_entitlements
        )

        # 计算可用额度
        remaining_credits = total_credits - consumed_credits
        return {
            "total_credits": total_credits,
            "remaining_credits": remaining_credits,
            "consumed_credits": consumed_credits,
        }

    @staticmethod
    def reset_periodic_entitlements():
        """周期性重置权益（每月执行）
        处理：
        1. 免费用户的月度重置
        2. 年度订阅用户的月度重置
        3. LTD 计划用户的月度重置
        """
        current_time = datetime.now()

        try:
            # 分开处理不同类型的权益
            expired_entitlements = Entitlement.query.filter(
                Entitlement.is_recurring == True,
                Entitlement.valid_until <= current_time,
            ).all()

            for entitlement in expired_entitlements:
                # 根据权益类型处理
                new_entitlement = None
                if entitlement.source_type == EntitlementSource.SUBSCRIPTION:
                    # 处理订阅权益
                    subscription = Subscription.get_by_id(entitlement.source_id)
                    if (
                        subscription
                        and subscription.status == SubscriptionStatus.ACTIVE.value
                    ):
                        plan = Plan.get_by_id(subscription.plan_id)
                        if plan.interval.lower() == "month":
                            # 跳过月度订阅（由 webhook 处理）
                            logger.info(
                                f"Skipping monthly subscription entitlement {entitlement.id} for user {entitlement.user_id}"
                            )
                            continue
                        # 处理年度订阅
                        new_entitlement = EntitlementService.create_from_subscription(
                            subscription, reference_time=current_time
                        )
                elif entitlement.source_type == EntitlementSource.FREE_PLAN:
                    # 处理免费计划权益
                    new_entitlement = EntitlementService.create_free_plan_entitlement(
                        entitlement.user_id, reference_time=current_time
                    )
                elif entitlement.source_type == EntitlementSource.LTD:
                    # 处理 LTD 计划权益
                    purchase = Purchase.get_by_id(entitlement.source_id)
                    if purchase:
                        # 创建新的 LTD 权益
                        new_entitlement = EntitlementService.create_from_ltd_purchase(
                            purchase, reference_time=current_time
                        )

                # 只有确定需要重置的权益才进行处理
                if new_entitlement:
                    # 1. 处理旧权益
                    entitlement.is_recurring = False
                    entitlement.updated_time = current_time
                    logger.info(
                        f"Marked {entitlement.source_type} entitlement {entitlement.id} as non-recurring"
                    )

                    # 2. 添加新权益
                    db.session.add(new_entitlement)
                    logger.info(
                        f"Created new {new_entitlement.source_type} entitlement {new_entitlement.id} "
                        f"for user {entitlement.user_id}"
                    )

            # 所有处理都成功后才提交
            db.session.commit()

        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to reset periodic entitlements: {str(e)}")
            raise  # 向上层抛出异常，让调用方知道有错误发生

    @staticmethod
    def handle_subscription_expiration(subscription_id, user_id, termination_time=None):
        """处理订阅过期的权益变更"""
        if termination_time is None:
            termination_time = datetime.now()

        try:
            # 1. 终止现有订阅权益
            EntitlementService.terminate_subscription_entitlements(
                subscription_id, termination_time=termination_time
            )
            logger.info(
                f"Terminated all entitlements for expired subscription {subscription_id}"
            )

            # 2. 创建免费计划权益
            free_entitlement = EntitlementService.create_free_plan_entitlement(user_id)
            if free_entitlement:
                db.session.add(free_entitlement)
                logger.info(f"Created free plan entitlement for user {user_id}")
                return free_entitlement
            else:
                logger.error(
                    f"Failed to create free plan entitlement for user {user_id}"
                )
                return None

        except Exception as e:
            logger.error(
                f"Failed to handle entitlement expiration for subscription {subscription_id}: {str(e)}"
            )
            raise

    @staticmethod
    def handle_subscription_payment(subscription, current_time=None):
        if current_time is None:
            current_time = datetime.now()

        try:
            # 1. 幂等性检查
            existing_entitlement = Entitlement.query.filter(
                Entitlement.source_type == EntitlementSource.SUBSCRIPTION,
                Entitlement.source_id == subscription.id,
                Entitlement.valid_from >= subscription.current_period_start,
                Entitlement.valid_until <= subscription.current_period_end,
            ).first()

            plan = Plan.get_by_id(subscription.plan_id)
            if (
                existing_entitlement
                and existing_entitlement.total_credits == plan.credit_amount
            ):
                logger.info(
                    f"Entitlement already exists for period: "
                    f"{subscription.current_period_start} - {subscription.current_period_end}"
                )
                return existing_entitlement

            # 2. 获取所有当前有效的权益
            current_entitlements = Entitlement.query.filter(
                Entitlement.user_id == subscription.user_id,
                Entitlement.valid_until > current_time,
            ).all()

            plan = Plan.query.get(subscription.plan_id)

            # 3. 处理现有权益
            for current_entitlement in current_entitlements:
                logger.info(
                    "current_entitlement: %s",
                    {
                        "id": current_entitlement.id,
                        "source_type": current_entitlement.source_type,
                        "source_id": current_entitlement.source_id,
                        "valid_from": current_entitlement.valid_from,
                        "valid_until": current_entitlement.valid_until,
                        "total_credits": current_entitlement.total_credits,
                        "consumed_credits": current_entitlement.consumed_credits,
                    },
                )
                logger.info(
                    "subscription: %s",
                    {
                        "id": subscription.id,
                        "current_period_start": subscription.current_period_start,
                        "current_period_end": subscription.current_period_end,
                    },
                )

                # 处理免费权益
                if current_entitlement.source_type == EntitlementSource.FREE_PLAN:
                    EntitlementService.terminate_entitlement(
                        current_entitlement, termination_time=current_time
                    )
                    logger.info(
                        f"Terminated free plan entitlement due to subscription: "
                        f"entitlement_id={current_entitlement.id}"
                    )
                # 处理订阅权益
                elif (
                    current_entitlement.source_type == EntitlementSource.SUBSCRIPTION
                    and current_entitlement.source_id == subscription.id
                ):
                    # 无论是升级还是续订，都在当前时间终止旧权益
                    EntitlementService.terminate_entitlement(
                        current_entitlement, termination_time=current_time
                    )
                    logger.info(
                        f"Terminated previous entitlement: "
                        f"subscription_id={subscription.id}, "
                        f"reason={'plan upgrade' if current_entitlement.total_credits < plan.credit_amount else 'plan downgrade' if current_entitlement.total_credits > plan.credit_amount else 'renewal'}, "
                        f"end_time={current_time}"
                    )

            # 4. 创建新权益
            new_entitlement = EntitlementService.create_from_subscription(
                subscription, reference_time=current_time
            )
            db.session.add(new_entitlement)
            logger.info(
                f"Created new entitlement for subscription {subscription.id}: "
                f"period={subscription.current_period_start} - {subscription.current_period_end}, "
                f"credits={plan.credit_amount}"
            )
            return new_entitlement

        except Exception as e:
            logger.error(
                f"Failed to handle entitlement for subscription {subscription.id}: {str(e)}"
            )
            raise

    @staticmethod
    def terminate_entitlement(entitlement, termination_time=None):
        """终止单个权益

        Args:
            entitlement: 要终止的权益记录
            termination_time: 终止时间，默认为当前时间
        """
        if termination_time is None:
            termination_time = datetime.now()

        if entitlement.valid_until <= termination_time:
            logger.info(
                f"Terminated entitlement {entitlement.id}: "
                f"user_id={entitlement.user_id}, "
                f"source_type={entitlement.source_type}, "
                f"source_id={entitlement.source_id}, "
                f"termination_time={termination_time}"
            )
            entitlement.is_recurring = False
            entitlement.valid_until = termination_time
            entitlement.updated_time = termination_time
            return

        entitlement.valid_until = termination_time
        entitlement.updated_time = termination_time
        entitlement.is_recurring = False
        logger.info(
            f"Terminated entitlement {entitlement.id}: "
            f"user_id={entitlement.user_id}, "
            f"source_type={entitlement.source_type}, "
            f"source_id={entitlement.source_id}, "
            f"termination_time={termination_time}"
        )

    @staticmethod
    def unlock_file(user_id, minutes_needed, file_id):
        """
        解锁文件，从用户权益中扣除相应分钟数

        Args:
            user_id: 用户ID
            minutes_needed: 需要解锁的分钟数
            file_id: 转录文件ID
        """
        try:
            # 检查配额
            credits = EntitlementService.get_user_credits(user_id)
            remaining_credits = credits["remaining_credits"]

            if remaining_credits < minutes_needed:
                raise InsufficientTranscriptionQuotaError(
                    f"Insufficient minutes to unlock file. Required: {minutes_needed}, Available: {remaining_credits}"
                )

            # 通过共享方法消费积分
            EntitlementService._consume_credits(
                user_id, minutes_needed, file_id, service_type="transcription_unlock"
            )

            db.session.commit()

            logger.info(
                f"Successfully unlocked file {file_id} for user {user_id}, "
                f"consuming {minutes_needed} minutes from entitlements"
            )

        except InsufficientTranscriptionQuotaError:
            db.session.rollback()
            logger.warning(
                f"Failed to unlock file {file_id} for user {user_id}: insufficient credits"
            )
            raise
        except Exception as e:
            db.session.rollback()
            logger.error(
                f"Failed to unlock file {file_id} for user {user_id}: {str(e)}"
            )
            raise

    @staticmethod
    def terminate_all_user_entitlements(user_id, termination_time=None):
        """终止用户的所有权益, 用于用户注销时调用

        Args:
            user_id: 用户ID
            termination_time: 终止时间，默认为当前时间

        Returns:
            int: 终止的权益数量
        """
        if termination_time is None:
            termination_time = datetime.now()

        # 获取用户的所有有效权益
        active_entitlements = Entitlement.get_active_entitlements(user_id)

        terminated_count = 0
        for entitlement in active_entitlements:
            EntitlementService.terminate_entitlement(entitlement, termination_time)
            terminated_count += 1

        logger.info(
            f"Terminated all {terminated_count} entitlements for user {user_id} due to account deactivation"
        )

        return terminated_count

    @staticmethod
    def _consume_credits(
        user_id, minutes_needed, file_id, service_type="transcription"
    ):
        """
        内部方法：从权益中消费分钟数并创建使用记录

        Args:
            user_id: 用户ID
            minutes_needed: 需要消费的分钟数
            file_id: 相关文件ID
            service_type: 服务类型，默认为"transcription"

        Returns:
            float: 实际消费的分钟数
        """
        active_entitlements = Entitlement.get_active_entitlements(user_id)
        total_consumed = 0

        # 按过期时间顺序使用所有 entitlements
        usage_records = []
        for entitlement in active_entitlements:
            if total_consumed >= minutes_needed:
                break

            remaining = entitlement.total_credits - entitlement.consumed_credits
            if remaining > 0:
                use_amount = min(remaining, minutes_needed - total_consumed)
                entitlement.consumed_credits += use_amount
                total_consumed += use_amount

                usage_records.append(
                    CreditUsage(
                        id=id_generator.get_id(),
                        user_id=user_id,
                        entitlement_id=entitlement.id,
                        related_file_id=file_id,
                        service_type=service_type,
                        credits_used=use_amount,
                        created_time=datetime.now(),
                    )
                )

        # 添加所有使用记录
        db.session.add_all(usage_records)
        return total_consumed
