"""Model selection service for transcription tasks."""

import logging

logger = logging.getLogger(__name__)


class ModelSelectionService:
    """Service for selecting appropriate models based on various criteria."""

    # 模型常量
    FAL_WHISPER = "fal-ai/whisper"
    FAL_WIZPER = "fal-ai/wizper"
    REPLICATE_WHISPERX = "replicate/whisperx"
    DEEPINFRA_WHISPER_TURBO = "deepinfra/whisper-turbo"
    DEEPINFRA_WHISPER = "deepinfra/whisper"

    # 模型分配比例配置
    MODEL_DISTRIBUTION = {
        FAL_WHISPER: 5,  # 5%, 保留少部分，确保功能可用
        DEEPINFRA_WHISPER: 0,  # 0%
        DEEPINFRA_WHISPER_TURBO: 95,  # 95
    }

    @classmethod
    def select_transcription_model(cls, user_id, transcription_file):
        """
        根据用户和转录类型选择合适的转录模型

        Args:
            user_id: 用户ID (保留参数以维持接口兼容性)
            transcription_file: 转录文件对象

        Returns:
            str: 选择的模型标识符
        """
        transcription_type = transcription_file.transcription_type
        # 规则1: 字幕类型一律用 deepinfra/whisper-turbo
        if transcription_type == "subtitle":
            return cls.select_model_by_file_id(transcription_file.id)

        if transcription_file.language_code in ["zh", "zh_tw"]:
            return cls.select_model_by_file_id(transcription_file.id)

        # 不管是匿名、免费、付费（含订阅、一次性、LTD），都用 fal/wizper
        # 不再使用 replicate/whisperx， 因为会丢失内容，但是还保留在 go 服务作为兜底策略。
        return cls.FAL_WIZPER

    @classmethod
    def select_model_by_file_id(cls, file_id):
        """
        基于文件ID取余的方式选择转录模型，按照预设的比例分配

        该方法会自动根据 MODEL_DISTRIBUTION 中配置的比例进行分配，
        无需在调整比例时修改代码逻辑

        Args:
            file_id: 转录文件ID

        Returns:
            str: 选择的模型标识符
        """
        # 取余数，范围是0-99
        remainder = file_id % 100

        # 计算累积概率并选择模型
        cumulative_prob = 0
        for model, probability in cls.MODEL_DISTRIBUTION.items():
            cumulative_prob += probability
            if remainder < cumulative_prob:
                return model

        # 如果所有概率加起来小于100（不应该发生），使用最后一个非零概率的模型
        for model in reversed(list(cls.MODEL_DISTRIBUTION.keys())):
            if cls.MODEL_DISTRIBUTION[model] > 0:
                return model

        # 兜底返回，如果所有模型概率都为0（极端情况）
        logger.warning("All model probabilities are 0 in MODEL_DISTRIBUTION")
        return cls.FAL_WHISPER
