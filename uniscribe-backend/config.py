import os
from constants.task import TaskFeature


class Config:
    DEBUG = False
    TESTING = False
    SQLALCHEMY_DATABASE_URI = os.getenv("SQLALCHEMY_DATABASE_URI")
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    HOST = "https://uniscribe.co"

    # 文件完整性验证配置
    FILE_INTEGRITY_CHECK = {
        "enable_graceful_degradation": True,  # 是否启用降级策略
        "timeout_seconds": 10,  # 超时时间（秒）
    }

    TENCENT_CLOUD = {
        "secret_id": os.getenv("TENCENT_CLOUD_SECRET_ID"),
        "secret_key": os.getenv("TENCENT_CLOUD_SECRET_KEY"),
        "bucket_name": "shiyin-transcribe-1252375760",
        "region": "ap-nanjing",
        "scheme": "https",
    }
    OPENAI = {
        "api_key": os.getenv("OPENAI_API_KEY"),
    }
    CLOUDFLARE_R2 = {
        "access_key": os.getenv("CLOUDFLARE_R2_ACCESS_KEY"),
        "secret_key": os.getenv("CLOUDFLARE_R2_SECRET_KEY"),
        "endpoint_url": os.getenv("CLOUDFLARE_R2_ENDPOINT_URL"),
        "bucket_name": "shiyin",
    }

    STORAGE_TYPE = "s3"  # cos or s3(a.k.a cloudflare r2)

    STRIPE = {
        "secret_key": os.getenv("STRIPE_SECRET_KEY"),
        "webhook_secret": os.getenv("STRIPE_WEBHOOK_SECRET"),
    }

    #  plan 和 price 的映射关系
    STRIPE_PLAN_PRICE_LOOKUP_KEYS = {
        "basic_monthly": "basic_monthly",
        "basic_yearly": "basic_yearly",
        "pro_monthly": "pro_monthly_20241117",
        "pro_yearly": "pro_yearly_20241117",
        "lite_one_time": "lite_one_time",
        "plus_one_time": "plus_one_time",
        "max_one_time": "max_one_time",
    }

    SUPABASE = {
        "url": os.getenv("SUPABASE_URL"),
        "anon_key": os.getenv("SUPABASE_ANON_KEY"),
        "jwt_secret": os.getenv("SUPABASE_JWT_SECRET"),
    }

    YOUTUBE_PROXY = os.getenv("YOUTUBE_PROXY")

    PLAN_FEATURES = {
        "Free": {
            "export_formats": ["txt", "docx", "pdf", "srt", "vtt", "csv"],
            "retention_period": 30,  # days
            "features": [
                TaskFeature.text_summary.name,
                TaskFeature.mind_map.name,
                TaskFeature.qa_extraction.name,
            ],
        },
        "Basic": {
            "export_formats": ["txt", "docx", "pdf", "srt", "vtt", "csv"],
            "retention_period": None,
            "features": [
                TaskFeature.text_summary.name,
                TaskFeature.mind_map.name,
                TaskFeature.qa_extraction.name,
            ],
        },
        "Pro": {
            "export_formats": ["txt", "docx", "pdf", "srt", "vtt", "csv"],
            "retention_period": None,
            "features": [
                TaskFeature.text_summary.name,
                TaskFeature.mind_map.name,
                TaskFeature.qa_extraction.name,
            ],
        },
        "Lite": {
            "export_formats": ["txt", "docx", "pdf", "srt", "vtt", "csv"],
            "retention_period": None,  # days
            "features": [
                TaskFeature.text_summary.name,
                TaskFeature.mind_map.name,
                TaskFeature.qa_extraction.name,
            ],
        },
        "Plus": {
            "export_formats": ["txt", "docx", "pdf", "srt", "vtt", "csv"],
            "retention_period": None,
            "features": [
                TaskFeature.text_summary.name,
                TaskFeature.mind_map.name,
                TaskFeature.qa_extraction.name,
            ],
        },
        "Max": {
            "export_formats": ["txt", "docx", "pdf", "srt", "vtt", "csv"],
            "retention_period": None,
            "features": [
                TaskFeature.text_summary.name,
                TaskFeature.mind_map.name,
                TaskFeature.qa_extraction.name,
            ],
        },
        "Tier 1": {
            "export_formats": ["txt", "docx", "pdf", "srt", "vtt", "csv"],
            "retention_period": None,
            "features": [
                TaskFeature.text_summary.name,
                TaskFeature.mind_map.name,
                TaskFeature.qa_extraction.name,
            ],
        },
        "Tier 2": {
            "export_formats": ["txt", "docx", "pdf", "srt", "vtt", "csv"],
            "retention_period": None,
            "features": [
                TaskFeature.text_summary.name,
                TaskFeature.mind_map.name,
                TaskFeature.qa_extraction.name,
            ],
        },
    }
    AXIOM = {
        "token": os.getenv("AXIOM_TOKEN"),
        "dataset_name": "uniscribe-backend-prod",
    }
    RESEND = {
        "api_key": os.getenv("RESEND_API_KEY"),
    }

    USERCHECK = {
        "api_key": os.getenv("USERCHECK_API_KEY"),
    }

    # AppSumo 配置
    APPSUMO = {
        "client_id": os.getenv("APPSUMO_CLIENT_ID"),
        "client_secret": os.getenv("APPSUMO_CLIENT_SECRET"),
        "redirect_uri": os.getenv("APPSUMO_REDIRECT_URI"),
        "api_key": os.getenv("APPSUMO_API_KEY"),
        "webhook_url": os.getenv(
            "APPSUMO_WEBHOOK_URL", "http://app:8000/webhook/appsumo"
        ),
    }

    # AppSumo 计划映射关系
    APPSUMO_PLAN_MAPPING = {
        "tier1": 9,  # AppSumo tier1 对应 ID 为 9 的 LTD 计划
        "tier2": 10,  # AppSumo tier2 对应 ID 为 10 的 LTD 计划
    }

    # 邮箱域名验证缓存时间(7天)
    EMAIL_DOMAIN_CACHE_TTL = 7 * 24 * 3600

    # 邮箱域名白名单
    WHITELIST_DOMAINS = [
        "gmail.com",
        "outlook.com",
        "hotmail.com",
        "yahoo.com",
        "live.com",
        "icloud.com",
        "aol.com",
    ]


class ProductionConfig(Config):
    # azure mysql 需要 ssl 连接
    BASE_DIR = os.path.abspath(os.path.dirname(__file__))
    SQLALCHEMY_ENGINE_OPTIONS = {
        "connect_args": {
            "ssl": {"ca": os.path.join(BASE_DIR, "cert", "DigiCertGlobalRootCA.pem")},
        }
    }
    REDIS = {
        "host": os.getenv("REDIS_HOST"),
        "port": os.getenv("REDIS_PORT"),
        "password": os.getenv("REDIS_PASSWORD"),
        "ssl": True,
    }

    # 生产环境的一次性付款 promo code 映射关系
    STRIPE_PROMO_CODE_LOOKUP_KEYS = {
        # "lite_one_time": "CXQVI8C9", # lite 不需要 promo code
        "plus_one_time": "5X67NDLV",
        "max_one_time": "KBZCMHX0",
    }
    CLOUDFLARE_R2 = {
        "access_key": os.getenv("CLOUDFLARE_R2_ACCESS_KEY"),
        "secret_key": os.getenv("CLOUDFLARE_R2_SECRET_KEY"),
        "endpoint_url": os.getenv("CLOUDFLARE_R2_ENDPOINT_URL"),
        "bucket_name": "shiyin",
    }


class DevelopmentConfig(Config):
    DEBUG = True
    HOST = "http://127.0.0.1:3000"
    REDIS = {
        "host": os.getenv("REDIS_HOST"),
        "port": os.getenv("REDIS_PORT"),
        "password": os.getenv("REDIS_PASSWORD"),
        "ssl": False,
    }

    # 开发环境的一次性付款 promo code 映射关系
    STRIPE_PROMO_CODE_LOOKUP_KEYS = {
        # "lite_one_time": "TSB5RZHP", # lite 不需要 promo code
        "plus_one_time": "HOVK7KCX",
        "max_one_time": "FQE7VMFS",
    }
    CLOUDFLARE_R2 = {
        "access_key": os.getenv("CLOUDFLARE_R2_ACCESS_KEY"),
        "secret_key": os.getenv("CLOUDFLARE_R2_SECRET_KEY"),
        "endpoint_url": os.getenv("CLOUDFLARE_R2_ENDPOINT_URL"),
        "bucket_name": "uniscribe-dev",
    }


class TestingConfig(Config):
    TESTING = True


CONFIG_MAP = {
    "production": ProductionConfig,
    "development": DevelopmentConfig,
    "testing": TestingConfig,
}

env = os.environ.get("APP_SETTINGS", "development")
CONFIG = CONFIG_MAP[env]


def is_development_env():
    return env == "development"


def is_production_env():
    return env == "production"


def is_testing_env():
    return env == "testing"
